import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import App from './App';
import { store } from './store';
import { initializeTokenManagement } from './features/auth/utils/tokenManager';
import { setUser, setTokens } from './store/slices/authSlice';
import './styles/globals.css';

const rootElement = document.getElementById('root');
if (!rootElement) throw new Error('Failed to find the root element');

document.documentElement.classList.add('dark');

// Initialize token management and restore auth state
// Temporarily disabled for debugging
// const tokens = initializeTokenManagement();
// if (tokens) {
//   // Restore tokens to Redux state
//   store.dispatch(setTokens(tokens));
//
//   // TODO: In a real app, you might want to validate the token with the server
//   // and restore user data. For now, we'll just mark as authenticated if tokens exist
//   // You could decode the token to get user info or make an API call to get current user
// }

const root = ReactDOM.createRoot(rootElement);
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </React.StrictMode>
);
