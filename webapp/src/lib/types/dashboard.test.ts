import { describe, it, expect } from 'vitest';

// Import types to test they exist and are properly defined
import type { 
  Invoice, 
  Payment, 
  DashboardData, 
  InvoiceStatus, 
  PaymentStatus 
} from './dashboard';

describe('dashboard types', () => {
  it('should have correct InvoiceStatus values', () => {
    const statuses: InvoiceStatus[] = ['DRAFT', 'PENDING', 'PAID', 'EXPIRED'];
    expect(statuses).toHaveLength(4);
  });

  it('should have correct PaymentStatus values', () => {
    const statuses: PaymentStatus[] = ['PENDING', 'PAID', 'FAILED'];
    expect(statuses).toHaveLength(3);
  });

  it('should create valid Invoice object', () => {
    const invoice: Invoice = {
      id: '1',
      amount: 100,
      currency: 'USD',
      status: 'PENDING',
      created_at: Date.now(),
      email: '<EMAIL>',
      payment_url: 'https://example.com/pay'
    };
    
    expect(invoice.id).toBe('1');
    expect(invoice.amount).toBe(100);
    expect(invoice.status).toBe('PENDING');
  });

  it('should create valid Payment object', () => {
    const payment: Payment = {
      id: '1',
      amount: 100,
      currency: 'USD',
      status: 'PAID',
      created_at: Date.now(),
      invoice_id: 'INV-001',
      email: '<EMAIL>'
    };
    
    expect(payment.id).toBe('1');
    expect(payment.amount).toBe(100);
    expect(payment.status).toBe('PAID');
  });
});
