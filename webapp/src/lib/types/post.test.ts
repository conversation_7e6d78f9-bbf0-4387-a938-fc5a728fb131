import { describe, it, expect } from 'vitest';
import type { Post } from './post';

describe('post types', () => {
  it('should create valid Post object', () => {
    const post: Post = {
      id: 1,
      title: 'Test Post',
      body: 'This is a test post',
      userId: 1
    };
    
    expect(post.id).toBe(1);
    expect(post.title).toBe('Test Post');
    expect(post.body).toBe('This is a test post');
    expect(post.userId).toBe(1);
  });

  it('should have required properties', () => {
    const post: Post = {
      id: 2,
      title: 'Another Post',
      body: 'Another test post',
      userId: 2
    };
    
    expect(typeof post.id).toBe('number');
    expect(typeof post.title).toBe('string');
    expect(typeof post.body).toBe('string');
    expect(typeof post.userId).toBe('number');
  });
});
