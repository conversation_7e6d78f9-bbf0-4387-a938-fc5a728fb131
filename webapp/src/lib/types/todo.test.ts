import { describe, it, expect } from 'vitest';
import type { Todo } from './todo';

describe('todo types', () => {
  it('should create valid Todo object', () => {
    const todo: Todo = {
      id: 1,
      title: 'Test Todo',
      completed: false,
      userId: 1
    };
    
    expect(todo.id).toBe(1);
    expect(todo.title).toBe('Test Todo');
    expect(todo.completed).toBe(false);
    expect(todo.userId).toBe(1);
  });

  it('should handle completed todo', () => {
    const todo: Todo = {
      id: 2,
      title: 'Completed Todo',
      completed: true,
      userId: 2
    };
    
    expect(todo.completed).toBe(true);
  });

  it('should have required properties', () => {
    const todo: Todo = {
      id: 3,
      title: 'Another Todo',
      completed: false,
      userId: 3
    };
    
    expect(typeof todo.id).toBe('number');
    expect(typeof todo.title).toBe('string');
    expect(typeof todo.completed).toBe('boolean');
    expect(typeof todo.userId).toBe('number');
  });
});
