import { describe, it, expect } from 'vitest';
import { getBreadcrumbsForRoute, breadcrumbRoutes } from './breadcrumbs';

describe('breadcrumbs', () => {
  describe('getBreadcrumbsForRoute', () => {
    it('generates breadcrumbs for dashboard', () => {
      const breadcrumbs = getBreadcrumbsForRoute('/dashboard');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' }
      ]);
    });

    it('generates breadcrumbs for nested paths', () => {
      const breadcrumbs = getBreadcrumbsForRoute('/dashboard/pos');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'POS', href: '/dashboard/pos' }
      ]);
    });

    it('generates breadcrumbs for POS devices', () => {
      const breadcrumbs = getBreadcrumbsForRoute('/dashboard/pos/devices');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'POS', href: '/dashboard/pos' },
        { label: 'POS devices', href: '/dashboard/pos/devices' }
      ]);
    });

    it('generates breadcrumbs for dynamic device route', () => {
      const breadcrumbs = getBreadcrumbsForRoute('/dashboard/pos/devices/123', { deviceId: '123' }, 'My Device');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'POS', href: '/dashboard/pos' },
        { label: 'POS devices', href: '/dashboard/pos/devices' },
        { label: 'My Device', href: '/dashboard/pos/devices/123' }
      ]);
    });

    it('generates fallback breadcrumbs for unknown routes', () => {
      const breadcrumbs = getBreadcrumbsForRoute('/unknown/path');
      expect(breadcrumbs).toEqual([
        { label: 'Unknown', href: '/unknown' },
        { label: 'Path', href: '/unknown/path' }
      ]);
    });

    it('handles empty pathname', () => {
      const breadcrumbs = getBreadcrumbsForRoute('');
      expect(breadcrumbs).toEqual([]);
    });

    it('handles root pathname', () => {
      const breadcrumbs = getBreadcrumbsForRoute('/');
      expect(breadcrumbs).toEqual([]);
    });
  });

  describe('breadcrumbRoutes', () => {
    it('should have correct route patterns', () => {
      expect(breadcrumbRoutes).toHaveLength(4);
      expect(breadcrumbRoutes[0].pattern).toBe('/dashboard');
      expect(breadcrumbRoutes[1].pattern).toBe('/dashboard/pos');
      expect(breadcrumbRoutes[2].pattern).toBe('/dashboard/pos/devices');
      expect(breadcrumbRoutes[3].pattern).toBe('/dashboard/pos/devices/:deviceId');
    });
  });
});
