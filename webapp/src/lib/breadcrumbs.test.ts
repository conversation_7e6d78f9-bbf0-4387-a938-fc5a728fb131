import { describe, it, expect } from 'vitest';
import { generateBreadcrumbs } from './breadcrumbs';

describe('breadcrumbs', () => {
  describe('generateBreadcrumbs', () => {
    it('generates breadcrumbs for dashboard root', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
      ]);
    });

    it('generates breadcrumbs for POS section', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard/pos');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'POS', href: '/dashboard/pos' },
      ]);
    });

    it('generates breadcrumbs for invoicing section', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard/pos/invoicing');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'POS', href: '/dashboard/pos' },
        { label: 'Invoicing', href: '/dashboard/pos/invoicing' },
      ]);
    });

    it('generates breadcrumbs for specific invoice', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard/pos/invoicing/123');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'POS', href: '/dashboard/pos' },
        { label: 'Invoicing', href: '/dashboard/pos/invoicing' },
        { label: '#123', href: '/dashboard/pos/invoicing/123' },
      ]);
    });

    it('generates breadcrumbs for payments section', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard/payments');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Payments', href: '/dashboard/payments' },
      ]);
    });

    it('generates breadcrumbs for users section', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard/users');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Users', href: '/dashboard/users' },
      ]);
    });

    it('generates breadcrumbs for specific user', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard/users/456');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Users', href: '/dashboard/users' },
        { label: 'User 456', href: '/dashboard/users/456' },
      ]);
    });

    it('generates breadcrumbs for posts section', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard/posts');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Posts', href: '/dashboard/posts' },
      ]);
    });

    it('handles root path', () => {
      const breadcrumbs = generateBreadcrumbs('/');
      expect(breadcrumbs).toEqual([]);
    });

    it('handles empty path', () => {
      const breadcrumbs = generateBreadcrumbs('');
      expect(breadcrumbs).toEqual([]);
    });

    it('handles non-dashboard paths', () => {
      const breadcrumbs = generateBreadcrumbs('/login');
      expect(breadcrumbs).toEqual([]);
    });

    it('handles paths with trailing slash', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard/pos/');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'POS', href: '/dashboard/pos' },
      ]);
    });

    it('handles paths with query parameters', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard/pos/invoicing?page=2');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'POS', href: '/dashboard/pos' },
        { label: 'Invoicing', href: '/dashboard/pos/invoicing' },
      ]);
    });

    it('handles deep nested paths', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard/pos/invoicing/123/edit');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'POS', href: '/dashboard/pos' },
        { label: 'Invoicing', href: '/dashboard/pos/invoicing' },
        { label: '#123', href: '/dashboard/pos/invoicing/123' },
        { label: 'Edit', href: '/dashboard/pos/invoicing/123/edit' },
      ]);
    });

    it('capitalizes path segments correctly', () => {
      const breadcrumbs = generateBreadcrumbs('/dashboard/some-section');
      expect(breadcrumbs).toEqual([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Some-section', href: '/dashboard/some-section' },
      ]);
    });
  });
});
