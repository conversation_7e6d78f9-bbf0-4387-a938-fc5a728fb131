export const PAYMENT_STATUS = {
  text: {
    PAID: 'Completed',
    PENDING: 'Pending',
    EXPIRED: 'Expired',
    CANCELED: 'Canceled',
    UNDERPAID: 'Underpaid',
    REFUNDED: 'Refunded',
    FAILED: 'Failed',
    BLOCKED: 'Blocked',
  },
  colors: {
    PAID: 'bg-success',
    PENDING: 'bg-warning',
    EXPIRED: 'bg-error',
    CANCELED: 'bg-warning',
    UNDERPAID: 'bg-error',
    REFUNDED: 'bg-warning',
    FAILED: 'bg-error',
    BLOCKED: 'bg-error',
  },
};

export const INVOICE_STATUS = {
  text: {
    PAID: 'Paid',
    PENDING: 'Pending payment',
    EXPIRED: 'Expired',
    DRAFT: 'Draft',
  },
  colors: {
    PAID: 'bg-[#4ADE80] text-black border-[#4ADE80]',
    PENDING: 'bg-[#FCD34D] text-black border-[#FCD34D]',
    EXPIRED: 'bg-red-500 text-white border-red-500',
    DRAFT: 'bg-transparent text-gray-400 border-gray-400',
  },
};

export const POS_DEVICE_STATUS = {
  text: {
    PAIRED: 'Paired',
    UNPAIRED: 'Not Paired',
  },
  colors: {
    PAIRED: 'bg-success',
    UNPAIRED: 'bg-muted border-error text-error',
  },
};

export function getPaymentStatus(status: string | undefined) {
  return PAYMENT_STATUS.text[status as keyof typeof PAYMENT_STATUS.text] ?? '';
}

export function getPaymentStatusColor(status: string | undefined) {
  return PAYMENT_STATUS.colors[status as keyof typeof PAYMENT_STATUS.colors] ?? '';
}

export function getPosDeviceStatus(status: string | undefined) {
  return POS_DEVICE_STATUS.text[status as keyof typeof POS_DEVICE_STATUS.text] ?? '';
}

export function getPosDeviceStatusColor(status: string | undefined) {
  return POS_DEVICE_STATUS.colors[status as keyof typeof POS_DEVICE_STATUS.colors] ?? '';
}

export function getInvoiceStatus(status: string | undefined) {
  return INVOICE_STATUS.text[status as keyof typeof INVOICE_STATUS.text] ?? '';
}

export function getInvoiceStatusColor(status: string | undefined) {
  return INVOICE_STATUS.colors[status as keyof typeof INVOICE_STATUS.colors] ?? '';
}
