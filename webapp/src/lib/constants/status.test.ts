import { describe, it, expect } from 'vitest';
import { 
  getPaymentStatus, 
  getPaymentStatusColor, 
  getPosDeviceStatus, 
  getPosDeviceStatusColor, 
  getInvoiceStatus, 
  getInvoiceStatusColor 
} from './status';

describe('status constants', () => {
  describe('getPaymentStatus', () => {
    it('returns correct text for PAID status', () => {
      expect(getPaymentStatus('PAID')).toBe('Completed');
    });

    it('returns correct text for PENDING status', () => {
      expect(getPaymentStatus('PENDING')).toBe('Pending');
    });

    it('returns correct text for FAILED status', () => {
      expect(getPaymentStatus('FAILED')).toBe('Failed');
    });

    it('returns empty string for unknown status', () => {
      expect(getPaymentStatus('UNKNOWN')).toBe('');
    });

    it('returns empty string for undefined status', () => {
      expect(getPaymentStatus(undefined)).toBe('');
    });
  });

  describe('getPaymentStatusColor', () => {
    it('returns correct color for PAID status', () => {
      expect(getPaymentStatusColor('PAID')).toBe('bg-success');
    });

    it('returns correct color for PENDING status', () => {
      expect(getPaymentStatusColor('PENDING')).toBe('bg-warning');
    });

    it('returns correct color for FAILED status', () => {
      expect(getPaymentStatusColor('FAILED')).toBe('bg-error');
    });

    it('returns empty string for unknown status', () => {
      expect(getPaymentStatusColor('UNKNOWN')).toBe('');
    });

    it('returns empty string for undefined status', () => {
      expect(getPaymentStatusColor(undefined)).toBe('');
    });
  });

  describe('getPosDeviceStatus', () => {
    it('returns correct text for PAIRED status', () => {
      expect(getPosDeviceStatus('PAIRED')).toBe('Paired');
    });

    it('returns correct text for UNPAIRED status', () => {
      expect(getPosDeviceStatus('UNPAIRED')).toBe('Not Paired');
    });

    it('returns empty string for unknown status', () => {
      expect(getPosDeviceStatus('UNKNOWN')).toBe('');
    });

    it('returns empty string for undefined status', () => {
      expect(getPosDeviceStatus(undefined)).toBe('');
    });
  });

  describe('getPosDeviceStatusColor', () => {
    it('returns correct color for PAIRED status', () => {
      expect(getPosDeviceStatusColor('PAIRED')).toBe('bg-success');
    });

    it('returns correct color for UNPAIRED status', () => {
      expect(getPosDeviceStatusColor('UNPAIRED')).toBe('bg-muted border-error text-error');
    });

    it('returns empty string for unknown status', () => {
      expect(getPosDeviceStatusColor('UNKNOWN')).toBe('');
    });

    it('returns empty string for undefined status', () => {
      expect(getPosDeviceStatusColor(undefined)).toBe('');
    });
  });

  describe('getInvoiceStatus', () => {
    it('returns correct text for PAID status', () => {
      expect(getInvoiceStatus('PAID')).toBe('Paid');
    });

    it('returns correct text for PENDING status', () => {
      expect(getInvoiceStatus('PENDING')).toBe('Pending payment');
    });

    it('returns correct text for DRAFT status', () => {
      expect(getInvoiceStatus('DRAFT')).toBe('Draft');
    });

    it('returns empty string for unknown status', () => {
      expect(getInvoiceStatus('UNKNOWN')).toBe('');
    });

    it('returns empty string for undefined status', () => {
      expect(getInvoiceStatus(undefined)).toBe('');
    });
  });

  describe('getInvoiceStatusColor', () => {
    it('returns correct color for PAID status', () => {
      expect(getInvoiceStatusColor('PAID')).toBe('bg-transparent text-[#FFDB9C] border-[#FFDB9C]');
    });

    it('returns correct color for PENDING status', () => {
      expect(getInvoiceStatusColor('PENDING')).toBe('bg-[#FCD34D] text-black border-[#FCD34D]');
    });

    it('returns correct color for DRAFT status', () => {
      expect(getInvoiceStatusColor('DRAFT')).toBe('bg-transparent text-gray-400 border-gray-400');
    });

    it('returns empty string for unknown status', () => {
      expect(getInvoiceStatusColor('UNKNOWN')).toBe('');
    });

    it('returns empty string for undefined status', () => {
      expect(getInvoiceStatusColor(undefined)).toBe('');
    });
  });
});
