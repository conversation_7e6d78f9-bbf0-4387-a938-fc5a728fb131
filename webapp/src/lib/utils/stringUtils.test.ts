import { describe, it, expect } from 'vitest';
import { truncateAddress, hasIncompleteFields } from './stringUtils';

describe('stringUtils', () => {
  describe('truncateAddress', () => {
    it('truncates long addresses correctly', () => {
      const address = '0x1234567890abcdef1234567890abcdef12345678';
      expect(truncateAddress(address)).toBe('0x12345...45678');
    });

    it('returns short addresses unchanged', () => {
      const shortAddress = '0x123';
      expect(truncateAddress(shortAddress)).toBe('0x123');
    });

    it('handles empty string', () => {
      expect(truncateAddress('')).toBe('');
    });

    it('handles addresses exactly at threshold length', () => {
      const address = '0x123456789012'; // 14 characters
      expect(truncateAddress(address)).toBe('0x123456789012');
    });

    it('handles addresses just over threshold', () => {
      const address = '0x1234567890123'; // 15 characters
      expect(truncateAddress(address)).toBe('0x12345...90123');
    });

    it('handles custom start and end lengths', () => {
      const address = '0x1234567890abcdef1234567890abcdef12345678';
      expect(truncateAddress(address, 8, 6)).toBe('0x123456...345678');
    });
  });

  describe('hasIncompleteFields', () => {
    const completeInvoice = {
      id: '1',
      invoice_id: '#3213',
      status: 'PENDING' as const,
      email: '<EMAIL>',
      cc_emails: ['<EMAIL>'],
      items: [
        {
          id: '1',
          name: 'Item 1',
          price: 100,
          quantity: 1,
          amount: 100,
        },
      ],
      total: 100,
      currency: 'USD',
      payment_url: 'https://example.com/pay',
      created_at: Date.now(),
      due_date: Date.now() + 86400000,
    };

    it('returns false for complete invoice', () => {
      expect(hasIncompleteFields(completeInvoice)).toBe(false);
    });

    it('returns true when email is missing', () => {
      const incompleteInvoice = { ...completeInvoice, email: '' };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });

    it('returns true when email is undefined', () => {
      const incompleteInvoice = { ...completeInvoice, email: undefined as any };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });

    it('returns true when items array is empty', () => {
      const incompleteInvoice = { ...completeInvoice, items: [] };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });

    it('returns true when items is undefined', () => {
      const incompleteInvoice = { ...completeInvoice, items: undefined as any };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });

    it('returns true when total is zero', () => {
      const incompleteInvoice = { ...completeInvoice, total: 0 };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });

    it('returns true when total is undefined', () => {
      const incompleteInvoice = { ...completeInvoice, total: undefined as any };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });

    it('returns true when currency is missing', () => {
      const incompleteInvoice = { ...completeInvoice, currency: '' };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });

    it('returns true when currency is undefined', () => {
      const incompleteInvoice = { ...completeInvoice, currency: undefined as any };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });

    it('returns true when item has missing name', () => {
      const incompleteInvoice = {
        ...completeInvoice,
        items: [{ ...completeInvoice.items[0], name: '' }],
      };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });

    it('returns true when item has zero price', () => {
      const incompleteInvoice = {
        ...completeInvoice,
        items: [{ ...completeInvoice.items[0], price: 0 }],
      };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });

    it('returns true when item has zero quantity', () => {
      const incompleteInvoice = {
        ...completeInvoice,
        items: [{ ...completeInvoice.items[0], quantity: 0 }],
      };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });

    it('handles multiple incomplete fields', () => {
      const incompleteInvoice = {
        ...completeInvoice,
        email: '',
        total: 0,
        currency: '',
        items: [],
      };
      expect(hasIncompleteFields(incompleteInvoice)).toBe(true);
    });
  });
});
