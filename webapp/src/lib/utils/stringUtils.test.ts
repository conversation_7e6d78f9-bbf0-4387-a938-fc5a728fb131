import { describe, it, expect } from 'vitest';
import { safeRender, hasIncompleteFields } from './stringUtils';

describe('stringUtils', () => {
  describe('safeRender', () => {
    it('returns value when it is valid string', () => {
      expect(safeRender('hello')).toBe('hello');
    });

    it('returns fallback when value is null', () => {
      expect(safeRender(null)).toBe('—');
    });

    it('returns fallback when value is undefined', () => {
      expect(safeRender(undefined)).toBe('—');
    });

    it('returns fallback when value is empty string', () => {
      expect(safeRender('')).toBe('—');
    });

    it('returns fallback when value is whitespace only', () => {
      expect(safeRender('   ')).toBe('—');
    });

    it('returns custom fallback', () => {
      expect(safeRender(null, 'N/A')).toBe('N/A');
    });

    it('returns number values as is', () => {
      expect(safeRender(42)).toBe(42);
    });

    it('returns boolean values as is', () => {
      expect(safeRender(true)).toBe(true);
      expect(safeRender(false)).toBe(false);
    });

    it('returns object values as is', () => {
      const obj = { test: 'value' };
      expect(safeRender(obj)).toBe(obj);
    });
  });

  describe('hasIncompleteFields', () => {
    it('returns false when all fields are complete', () => {
      const obj = { name: 'John', age: 30, active: true };
      expect(hasIncompleteFields(obj)).toBe(false);
    });

    it('returns true when a field is null', () => {
      const obj = { name: 'John', age: null, active: true };
      expect(hasIncompleteFields(obj)).toBe(true);
    });

    it('returns true when a field is undefined', () => {
      const obj = { name: 'John', age: undefined, active: true };
      expect(hasIncompleteFields(obj)).toBe(true);
    });

    it('returns true when a field is empty string', () => {
      const obj = { name: '', age: 30, active: true };
      expect(hasIncompleteFields(obj)).toBe(true);
    });

    it('returns false for empty object', () => {
      expect(hasIncompleteFields({})).toBe(false);
    });

    it('handles objects with zero values correctly', () => {
      const obj = { name: 'John', age: 0, active: false };
      expect(hasIncompleteFields(obj)).toBe(false);
    });
  });
});
