import { describe, it, expect } from 'vitest';
import { formatCurrency, formatFiatAmount } from './currency';

describe('currency utils', () => {
  describe('formatCurrency', () => {
    it('formats USD amounts correctly', () => {
      expect(formatCurrency(100)).toBe('$100.00');
    });

    it('formats decimal amounts correctly', () => {
      expect(formatCurrency(100.50)).toBe('$100.50');
    });

    it('formats zero correctly', () => {
      expect(formatCurrency(0)).toBe('$0.00');
    });

    it('formats negative amounts correctly', () => {
      expect(formatCurrency(-100)).toBe('-$100.00');
    });
  });

  describe('formatFiatAmount', () => {
    it('formats USD amounts correctly', () => {
      expect(formatFiatAmount(100, 'USD')).toBe('$100.00');
    });

    it('formats EUR amounts correctly', () => {
      expect(formatFiatAmount(100, 'EUR')).toBe('€100.00');
    });

    it('returns undefined when amount is undefined', () => {
      expect(formatFiatAmount(undefined, 'USD')).toBeUndefined();
    });

    it('returns undefined when currency is undefined', () => {
      expect(formatFiatAmount(100, undefined)).toBeUndefined();
    });

    it('returns undefined when both are undefined', () => {
      expect(formatFiatAmount(undefined, undefined)).toBeUndefined();
    });

    it('handles zero amount', () => {
      expect(formatFiatAmount(0, 'USD')).toBeUndefined();
    });
  });
});
