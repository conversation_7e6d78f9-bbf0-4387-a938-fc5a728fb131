import { describe, it, expect } from 'vitest';
import { formatFiatAmount, formatCryptoAmount } from './currency';

describe('currency utils', () => {
  describe('formatFiatAmount', () => {
    it('formats USD amounts correctly', () => {
      expect(formatFiatAmount(100, 'USD')).toBe('$100.00');
      expect(formatFiatAmount(1234.56, 'USD')).toBe('$1,234.56');
      expect(formatFiatAmount(0, 'USD')).toBe('$0.00');
    });

    it('formats AED amounts correctly', () => {
      expect(formatFiatAmount(100, 'AED')).toBe('AED 100.00');
      expect(formatFiatAmount(1234.56, 'AED')).toBe('AED 1,234.56');
      expect(formatFiatAmount(0, 'AED')).toBe('AED 0.00');
    });

    it('formats EUR amounts correctly', () => {
      expect(formatFiatAmount(100, 'EUR')).toBe('€100.00');
      expect(formatFiatAmount(1234.56, 'EUR')).toBe('€1,234.56');
      expect(formatFiatAmount(0, 'EUR')).toBe('€0.00');
    });

    it('handles unknown currencies with fallback', () => {
      expect(formatFiatAmount(100, 'XYZ')).toBe('XYZ 100.00');
      expect(formatFiatAmount(1234.56, 'UNKNOWN')).toBe('UNKNOWN 1,234.56');
    });

    it('handles negative amounts', () => {
      expect(formatFiatAmount(-100, 'USD')).toBe('-$100.00');
      expect(formatFiatAmount(-1234.56, 'AED')).toBe('-AED 1,234.56');
    });

    it('handles decimal precision correctly', () => {
      expect(formatFiatAmount(100.123, 'USD')).toBe('$100.12');
      expect(formatFiatAmount(100.999, 'USD')).toBe('$101.00');
    });
  });

  describe('formatCryptoAmount', () => {
    it('formats crypto amounts with correct precision', () => {
      expect(formatCryptoAmount(1.23456789, 'BTC')).toBe('1.23456789 BTC');
      expect(formatCryptoAmount(100.5, 'USDT')).toBe('100.5 USDT');
      expect(formatCryptoAmount(0, 'ETH')).toBe('0 ETH');
    });

    it('handles very small amounts', () => {
      expect(formatCryptoAmount(0.00000001, 'BTC')).toBe('0.00000001 BTC');
      expect(formatCryptoAmount(0.000123, 'ETH')).toBe('0.000123 ETH');
    });

    it('handles large amounts', () => {
      expect(formatCryptoAmount(1000000, 'USDT')).toBe('1000000 USDT');
      expect(formatCryptoAmount(999999.123456, 'BTC')).toBe('999999.123456 BTC');
    });

    it('handles negative amounts', () => {
      expect(formatCryptoAmount(-1.5, 'BTC')).toBe('-1.5 BTC');
      expect(formatCryptoAmount(-0.001, 'ETH')).toBe('-0.001 ETH');
    });

    it('removes trailing zeros', () => {
      expect(formatCryptoAmount(1.0, 'BTC')).toBe('1 BTC');
      expect(formatCryptoAmount(1.10, 'ETH')).toBe('1.1 ETH');
      expect(formatCryptoAmount(1.100000, 'USDT')).toBe('1.1 USDT');
    });
  });
});
