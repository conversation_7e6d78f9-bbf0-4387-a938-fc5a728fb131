import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils/tw';
import type { WithdrawHistoryItem } from '../types';

interface WithdrawHistoryTableProps {
  history: WithdrawHistoryItem[];
  className?: string;
}

const truncateAddress = (address: string): string => {
  if (address.length <= 20) return address;
  return `${address.slice(0, 10)}...${address.slice(-6)}`;
};

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'Success':
      return 'default';
    case 'Pending':
      return 'secondary';
    case 'Failed':
      return 'destructive';
    default:
      return 'outline';
  }
};

export function WithdrawHistoryTable({ history, className }: WithdrawHistoryTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const totalPages = Math.ceil(history.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = history.slice(startIndex, endIndex);

  return (
    <div className={cn('space-y-4', className)}>
      <div className="rounded-md border border-border/50">
        <Table>
          <TableHeader>
            <TableRow className="border-border/50">
              <TableHead className="text-muted-foreground">Date</TableHead>
              <TableHead className="text-muted-foreground">To</TableHead>
              <TableHead className="text-muted-foreground">Quantity</TableHead>
              <TableHead className="text-muted-foreground">FX Rate</TableHead>
              <TableHead className="text-muted-foreground">Fee</TableHead>
              <TableHead className="text-muted-foreground">Total</TableHead>
              <TableHead className="text-muted-foreground">Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentItems.map((item) => (
              <TableRow key={item.id} className="border-border/50">
                <TableCell className="text-foreground">
                  {item.date.toLocaleDateString('en-GB')}
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="text-foreground font-medium">
                      {item.toType}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {truncateAddress(item.to)}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="text-foreground">{item.quantity.toFixed(2)}</div>
                    <div className="text-xs text-muted-foreground">USDT (TRC-20)</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="text-foreground">{item.fxRate.toFixed(2)} AED</div>
                    <div className="text-xs text-muted-foreground">1 USDT (TRC-20)</div>
                  </div>
                </TableCell>
                <TableCell className="text-foreground">
                  {item.fee.toFixed(2)}
                </TableCell>
                <TableCell className="text-foreground">
                  {item.total.toFixed(2)}
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusVariant(item.status)}>
                    {item.status}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}

            {currentItems.map((item) =>
              item.description ? (
                <TableRow key={`${item.id}-desc`} className="border-border/50">
                  <TableCell colSpan={7} className="text-sm text-muted-foreground italic">
                    {item.description}
                  </TableCell>
                </TableRow>
              ) : null
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={cn(
                    'w-8 h-8 rounded text-sm font-medium transition-colors',
                    page === currentPage
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                  )}
                >
                  {page}
                </button>
              );
            })}

            {totalPages > 5 && (
              <>
                <span className="text-muted-foreground">...</span>
                <button
                  onClick={() => setCurrentPage(totalPages)}
                  className={cn(
                    'w-8 h-8 rounded text-sm font-medium transition-colors',
                    totalPages === currentPage
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                  )}
                >
                  {totalPages}
                </button>
              </>
            )}
          </div>

          <div className="text-sm text-muted-foreground">
            PAGE {currentPage} OF {totalPages}
          </div>
        </div>
      )}
    </div>
  );
}