import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { cn } from '@/lib/utils/tw';
import type { BankAccount } from '../types';

interface BankAccountsTableProps {
  accounts: BankAccount[];
  onAddAccount?: () => void;
  className?: string;
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'Active':
      return 'default';
    case 'Pending':
      return 'secondary';
    case 'Inactive':
      return 'outline';
    default:
      return 'outline';
  }
};

export function BankAccountsTable({
  accounts,
  onAddAccount,
  className
}: BankAccountsTableProps) {
  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-foreground">Bank accounts</h3>
        {onAddAccount && (
          <Button
            onClick={onAddAccount}
            size="sm"
            className="bg-primary/10 text-primary hover:bg-primary/20"
          >
            <Plus className="w-4 h-4 mr-2" />
            New bank account
          </Button>
        )}
      </div>

      <div className="rounded-md border border-border/50">
        <Table>
          <TableHeader>
            <TableRow className="border-border/50">
              <TableHead className="text-muted-foreground">IBAN</TableHead>
              <TableHead className="text-muted-foreground">STATUS</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {accounts.map((account) => (
              <TableRow key={account.id} className="border-border/50">
                <TableCell className="text-foreground font-medium">
                  {account.iban}
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusVariant(account.status)}>
                    {account.status}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}