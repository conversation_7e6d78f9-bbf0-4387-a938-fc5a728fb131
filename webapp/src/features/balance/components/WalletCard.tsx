import { Link } from '@tanstack/react-router';
import { cn } from '@/lib/utils/tw';
import type { WalletBalance } from '../types';

interface WalletCardProps {
  wallet: WalletBalance;
  className?: string;
}

const networkColors: Record<string, string> = {
  TRC20: 'bg-green-500',
  ERC20: 'bg-blue-500',
  BEP20: 'bg-yellow-500',
  Polygon: 'bg-purple-500',
  Arbitrum: 'bg-orange-500',
};

export function WalletCard({ wallet, className }: WalletCardProps) {
  const networkColor = networkColors[wallet.network] || 'bg-gray-500';

  return (
    <Link
      to="/dashboard/balance/wallet/$walletId"
      params={{ walletId: wallet.id }}
      className="block"
    >
      <div className={cn(
        'bg-gray-800/50 rounded-lg p-4 hover:bg-gray-800/70 transition-colors cursor-pointer border border-gray-700/50',
        className
      )}>
        <div className="flex items-center gap-4">
          <div className="relative">
            <div className={cn(
              'w-12 h-12 rounded-full flex items-center justify-center text-white font-semibold text-lg',
              networkColor
            )}>
              T
            </div>

            <div className={cn(
              'absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-gray-900',
              networkColor
            )} />
          </div>

          <div className="flex-1 min-w-0">
            <div className="text-sm text-gray-400 mb-1">
              {wallet.network} network
            </div>
            <div className="text-lg font-semibold text-white mb-1">
              {wallet.balance.toFixed(2)} {wallet.coinName}
            </div>
            <div className="text-sm text-gray-400">
              {wallet.fiatEquivalent.toFixed(4)} AED
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}