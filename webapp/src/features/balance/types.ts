export type NetworkType = 'TRC20' | 'ERC20' | 'BEP20' | 'Polygon' | 'Arbitrum';

export type CoinType = 'USDT' | 'USDC' | 'ETH' | 'BTC' | 'BNB';

export type WithdrawStatus = 'Pending' | 'Failed' | 'Success';

export type WithdrawType = 'Crypto wallet' | 'Bank account';

export interface WalletBalance {
  id: string;
  coinName: CoinType;
  network: NetworkType;
  balance: number;
  fiatEquivalent: number;
  walletAddress: string;
  isActive: boolean;
}

export interface TotalBalance {
  totalUSDT: number;
  totalAED: number;
  lastUpdated: Date;
}

export interface WithdrawHistoryItem {
  id: string;
  date: Date;
  to: string;
  toType: WithdrawType;
  quantity: number;
  fxRate: number;
  fee: number;
  total: number;
  status: WithdrawStatus;
  description?: string;
}

export interface WalletDetails extends WalletBalance {
  qrCode?: string;
  withdrawHistory: WithdrawHistoryItem[];
}

export interface BankAccount {
  id: string;
  iban: string;
  status: 'Active' | 'Pending' | 'Inactive';
  bankName?: string;
  accountHolder?: string;
}

export interface WithdrawWallet {
  id: string;
  name: string;
  address: string;
  network: NetworkType;
  isActive: boolean;
}

export interface BalanceResponse {
  wallets: WalletBalance[];
  totalBalance: TotalBalance;
  bankAccounts: BankAccount[];
  withdrawWallets: WithdrawWallet[];
}

export interface WalletDetailsResponse extends WalletDetails {
  // Additional fields that might come from API
}

export interface CreateWithdrawWalletForm {
  name: string;
  address: string;
  network: NetworkType;
}

export interface WithdrawToWalletForm {
  walletId: string;
  amount: number;
  destinationAddress: string;
  network: NetworkType;
}

export interface WithdrawToBankForm {
  bankAccountId: string;
  amount: number;
  currency: 'AED' | 'USD';
}