export type NetworkType = 'TRC20' | 'ERC20' | 'BEP20' | 'Polygon' | 'Arbitrum';

export type CoinType = 'USDT' | 'USDC' | 'ETH' | 'BTC' | 'BNB';

export type WithdrawStatus = 'Pending' | 'Failed' | 'Success';

export type WithdrawType = 'Crypto wallet' | 'Bank account';

export interface WalletBalance {
  id: string;
  coinName: CoinType;
  network: NetworkType;
  balance: number; // Balance in crypto
  fiatEquivalent: number; // Balance in AED
  walletAddress: string;
  isActive: boolean;
}

export interface TotalBalance {
  totalUSDT: number;
  totalAED: number;
  lastUpdated: Date;
}

export interface WithdrawHistoryItem {
  id: string;
  date: Date;
  to: string; // Destination (wallet address or bank account)
  toType: WithdrawType;
  quantity: number; // Amount in crypto
  fxRate: number; // Exchange rate at time of transaction
  fee: number; // Fee in AED
  total: number; // Total in AED
  status: WithdrawStatus;
  description?: string;
}

export interface WalletDetails extends WalletBalance {
  qrCode?: string; // QR code data for receiving crypto
  withdrawHistory: WithdrawHistoryItem[];
}

export interface BankAccount {
  id: string;
  iban: string;
  status: 'Active' | 'Pending' | 'Inactive';
  bankName?: string;
  accountHolder?: string;
}

export interface WithdrawWallet {
  id: string;
  name: string;
  address: string;
  network: NetworkType;
  isActive: boolean;
}

// API Response types
export interface BalanceResponse {
  wallets: WalletBalance[];
  totalBalance: TotalBalance;
  bankAccounts: BankAccount[];
  withdrawWallets: WithdrawWallet[];
}

export interface WalletDetailsResponse extends WalletDetails {
  // Additional fields that might come from API
}

// Form types for creating/editing
export interface CreateWithdrawWalletForm {
  name: string;
  address: string;
  network: NetworkType;
}

export interface WithdrawToWalletForm {
  walletId: string;
  amount: number;
  destinationAddress: string;
  network: NetworkType;
}

export interface WithdrawToBankForm {
  bankAccountId: string;
  amount: number;
  currency: 'AED' | 'USD';
}