import type {
  WalletBalance,
  TotalBalance,
  WithdrawHistoryItem,
  WalletDetails,
  BankAccount,
  WithdrawWallet,
  BalanceResponse,
} from './types';

export const mockWalletBalances: WalletBalance[] = [
  {
    id: 'wallet-1',
    coinName: 'USDT',
    network: 'TRC20',
    balance: 38.17,
    fiatEquivalent: 140.0639,
    walletAddress: '0xf234...a737f6',
    isActive: true,
  },
  {
    id: 'wallet-2',
    coinName: 'USDT',
    network: 'ERC20',
    balance: 38.17,
    fiatEquivalent: 140.0639,
    walletAddress: '0xe456...b849g7',
    isActive: true,
  },
  {
    id: 'wallet-3',
    coinName: 'USDT',
    network: 'BEP20',
    balance: 38.17,
    fiatEquivalent: 140.0639,
    walletAddress: '0xd789...c951h8',
    isActive: true,
  },
  {
    id: 'wallet-4',
    coinName: 'USDT',
    network: 'Polygon',
    balance: 45.23,
    fiatEquivalent: 165.8434,
    walletAddress: '0xa123...d062i9',
    isActive: true,
  },
  {
    id: 'wallet-5',
    coinName: 'USDT',
    network: 'Arbitrum',
    balance: 72.89,
    fiatEquivalent: 267.3671,
    walletAddress: '0xb456...e173j0',
    isActive: true,
  },
];

export const mockTotalBalance: TotalBalance = {
  totalUSDT: 272.1672,
  totalAED: 998.3423,
  lastUpdated: new Date('2025-01-15T10:30:00Z'),
};

export const mockBankAccounts: BankAccount[] = [
  {
    id: 'bank-1',
    iban: '***********************',
    status: 'Pending',
    bankName: 'Emirates NBD',
    accountHolder: 'Merchant Account',
  },
  {
    id: 'bank-2',
    iban: '***********************',
    status: 'Active',
    bankName: 'ADCB',
    accountHolder: 'Merchant Account',
  },
];

export const mockWithdrawWallets: WithdrawWallet[] = [
  {
    id: 'withdraw-1',
    name: '[Address name 1]',
    address: '0x1RGT7...3985',
    network: 'TRC20',
    isActive: true,
  },
  {
    id: 'withdraw-2',
    name: '[Address name 1]',
    address: '0x1RGT7...3985',
    network: 'ERC20',
    isActive: true,
  },
  {
    id: 'withdraw-3',
    name: '[Address name 1]',
    address: '0x1RGT7...3985',
    network: 'BEP20',
    isActive: true,
  },
];

export const mockWithdrawHistory: WithdrawHistoryItem[] = [
  {
    id: 'withdraw-history-1',
    date: new Date('2025-03-24'),
    to: '0xf234...a737f6',
    toType: 'Crypto wallet',
    quantity: 55.86,
    fxRate: 3.67,
    fee: 1.73,
    total: 58.15,
    status: 'Pending',
  },
  {
    id: 'withdraw-history-2',
    date: new Date('2025-03-24'),
    to: '0xf234...a737f6',
    toType: 'Crypto wallet',
    quantity: 55.86,
    fxRate: 3.67,
    fee: 1.73,
    total: 58.15,
    status: 'Failed',
  },
  {
    id: 'withdraw-history-3',
    date: new Date('2025-03-24'),
    to: '***********************',
    toType: 'Bank account',
    quantity: 55.86,
    fxRate: 3.67,
    fee: 1.73,
    total: 58.15,
    status: 'Success',
  },
  {
    id: 'withdraw-history-4',
    date: new Date('2025-03-24'),
    to: '***********************',
    toType: 'Bank account',
    quantity: 55.86,
    fxRate: 3.67,
    fee: 1.73,
    total: 58.15,
    status: 'Success',
  },
  {
    id: 'withdraw-history-5',
    date: new Date('2025-03-24'),
    to: '0xf234...a737f6',
    toType: 'Crypto wallet',
    quantity: 55.86,
    fxRate: 3.67,
    fee: 1.73,
    total: 58.15,
    status: 'Success',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
  },
  {
    id: 'withdraw-history-6',
    date: new Date('2025-03-24'),
    to: '0xf234...a737f6',
    toType: 'Crypto wallet',
    quantity: 55.86,
    fxRate: 3.67,
    fee: 1.73,
    total: 58.15,
    status: 'Success',
  },
  {
    id: 'withdraw-history-7',
    date: new Date('2025-03-24'),
    to: '0xf234...a737f6',
    toType: 'Crypto wallet',
    quantity: 55.86,
    fxRate: 3.67,
    fee: 1.73,
    total: 58.15,
    status: 'Success',
  },
  {
    id: 'withdraw-history-8',
    date: new Date('2025-03-24'),
    to: '0xf234...a737f6',
    toType: 'Crypto wallet',
    quantity: 55.86,
    fxRate: 3.67,
    fee: 1.73,
    total: 58.15,
    status: 'Success',
  },
];

export const mockWalletDetails: Record<string, WalletDetails> = {
  'wallet-1': {
    ...mockWalletBalances[0],
    qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    withdrawHistory: mockWithdrawHistory,
  },
  'wallet-2': {
    ...mockWalletBalances[1],
    qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    withdrawHistory: mockWithdrawHistory,
  },
  'wallet-3': {
    ...mockWalletBalances[2],
    qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    withdrawHistory: mockWithdrawHistory,
  },
  'wallet-4': {
    ...mockWalletBalances[3],
    qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    withdrawHistory: mockWithdrawHistory,
  },
  'wallet-5': {
    ...mockWalletBalances[4],
    qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    withdrawHistory: mockWithdrawHistory,
  },
};

export const mockBalanceResponse: BalanceResponse = {
  wallets: mockWalletBalances,
  totalBalance: mockTotalBalance,
  bankAccounts: mockBankAccounts,
  withdrawWallets: mockWithdrawWallets,
};

export const getWalletById = (id: string): WalletDetails | undefined => {
  return mockWalletDetails[id];
};

export const getWalletsByNetwork = (network: string) => {
  return mockWalletBalances.filter(wallet => wallet.network === network);
};

export const getTotalBalanceByNetwork = () => {
  const networkTotals = mockWalletBalances.reduce((acc, wallet) => {
    if (!acc[wallet.network]) {
      acc[wallet.network] = { crypto: 0, fiat: 0 };
    }
    acc[wallet.network].crypto += wallet.balance;
    acc[wallet.network].fiat += wallet.fiatEquivalent;
    return acc;
  }, {} as Record<string, { crypto: number; fiat: number }>);

  return networkTotals;
};