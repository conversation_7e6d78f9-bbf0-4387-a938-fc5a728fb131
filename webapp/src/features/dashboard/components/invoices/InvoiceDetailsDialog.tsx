import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Invoice } from '@/features/dashboard/types';
import { getInvoiceStatus, getInvoiceStatusColor } from '@/lib/constants/status';
import { cn } from '@/lib/utils';
import { formatFiatAmount } from '@/lib/utils/currency';
import { formatDate } from 'date-fns';
import { CopyButton } from '@/components/ui/copy-button';
import { useEffect, useState } from 'react';
import { toast } from '@/hooks/use-toast';
import { hasIncompleteFields } from '@/lib/utils/stringUtils';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface InvoiceDetailsDialogProps {
  invoice: Invoice | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function InvoiceDetailsDialog({ invoice, open, onOpenChange }: InvoiceDetailsDialogProps) {
  const [showSyncBanner, setShowSyncBanner] = useState(false);

  useEffect(() => {
    if (invoice && hasIncompleteFields(invoice)) {
      setShowSyncBanner(true);
    } else {
      setShowSyncBanner(false);
    }
  }, [invoice]);

  if (!invoice) return null;

  const isDraft = invoice.status === 'DRAFT';
  const hasPaymentUrl = Boolean(invoice.payment_url);
  const statusText = getInvoiceStatus(invoice.status);
  const statusColor = getInvoiceStatusColor(invoice.status);

  const handleResend = () => {
    if (isDraft) return;

    toast({
      title: 'Invoice Resent',
      description: `Invoice ${invoice.invoice_id} has been resent to ${invoice.email}`,
    });
  };

  const handleDuplicate = () => {
    toast({
      title: 'Invoice Duplicated',
      description: `A copy of invoice ${invoice.invoice_id} has been created`,
    });
  };

  const handleRefresh = () => {
    setShowSyncBanner(false);
    toast({
      title: 'Status Refreshed',
      description: 'Invoice status has been updated',
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>Invoice details - {invoice.invoice_id}</DialogTitle>
            <Badge className={cn('text-white', statusColor)}>{statusText}</Badge>
          </div>
          <DialogDescription>
            Dashboard &gt; POS &gt; Invoicing &gt; {invoice.invoice_id}
          </DialogDescription>
        </DialogHeader>

        {showSyncBanner && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>Status may be outdated. Refresh?</span>
              <Button variant="ghost" size="sm" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Date sent</Label>
              <p>{formatDate(new Date(invoice.created_at), 'dd-MM-yyyy HH:mm')}</p>
            </div>
            <div>
              <Label>Due date</Label>
              <p>{formatDate(new Date(invoice.due_date), 'dd-MM-yyyy')}</p>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Email address</Label>
            <p>{invoice.email}</p>
            {invoice.cc_emails && invoice.cc_emails.length > 0 && (
              <p className="text-sm text-muted-foreground">CC: {invoice.cc_emails.join(', ')}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label>Payment URL</Label>
            {hasPaymentUrl ? (
              <div className="flex items-center gap-2">
                <span className="font-mono text-sm bg-muted p-2 rounded flex-1 truncate">
                  {invoice.payment_url}
                </span>
                <CopyButton value={invoice.payment_url!} variant="outline" size="sm">
                  Copy
                </CopyButton>
              </div>
            ) : (
              <p className="text-muted-foreground">Link unavailable, try again later</p>
            )}
          </div>

          <div className="space-y-4">
            <Label>Order details</Label>
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="space-y-2">
                <div className="grid grid-cols-4 gap-4 text-sm font-medium text-muted-foreground">
                  <span>Item</span>
                  <span className="text-right">Price</span>
                  <span className="text-right">Qty</span>
                  <span className="text-right">Amount</span>
                </div>
                {invoice.items.map((item) => (
                  <div key={item.id} className="grid grid-cols-4 gap-4 text-sm">
                    <span>{item.name}</span>
                    <span className="text-right">
                      {formatFiatAmount(item.price, invoice.currency)}
                    </span>
                    <span className="text-right">{item.quantity}</span>
                    <span className="text-right">
                      {formatFiatAmount(item.amount, invoice.currency)}
                    </span>
                  </div>
                ))}
                <div className="border-t pt-2 mt-2">
                  <div className="grid grid-cols-4 gap-4 text-sm font-medium">
                    <span className="col-span-3 text-right">Order Total:</span>
                    <span className="text-right">
                      {formatFiatAmount(invoice.total, invoice.currency)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={handleResend} disabled={isDraft}>
              Resend
            </Button>
            <Button variant="outline" onClick={handleDuplicate}>
              Duplicate
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
