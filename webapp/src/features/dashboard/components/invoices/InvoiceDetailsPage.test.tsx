import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { InvoiceDetailsPage } from './InvoiceDetailsPage';
import { Invoice } from '@/features/dashboard/types';

vi.mock('@/hooks/use-toast', () => ({
  toast: vi.fn(),
}));

vi.mock('@/components/layouts/Page', () => ({
  Page: ({ title, actions, children }: { title: any; actions: any; children: any }) => (
    <div data-testid="page">
      <div data-testid="page-title">{title}</div>
      <div data-testid="page-actions">{actions}</div>
      <div data-testid="page-content">{children}</div>
    </div>
  ),
}));

vi.mock('@tanstack/react-router', () => ({
  Link: ({ to, children, className }: { to: string; children: any; className?: string }) => (
    <a href={to} className={className} data-testid="link">
      {children}
    </a>
  ),
}));

const mockInvoice: Invoice = {
  id: '1',
  invoice_id: '#3213',
  email: '<EMAIL>',
  cc_emails: ['<EMAIL>'],
  total: 200.0,
  currency: 'AED',
  status: 'PENDING',
  created_at: Date.now() - 86400000,
  due_date: Date.now() + 86400000,
  payment_url: 'https://checkout.wefi.solutions/email-order/1926a29a-d5ef-4af7-a0e6-f31...',
  items: [
    {
      id: '1',
      name: 'Item name 1',
      price: 100,
      quantity: 1,
      amount: 100,
    },
    {
      id: '2',
      name: 'Item name 2',
      price: 100,
      quantity: 1,
      amount: 100,
    },
  ],
};

describe('InvoiceDetailsPage', () => {
  it('renders invoice details correctly', () => {
    render(<InvoiceDetailsPage invoice={mockInvoice} />);

    expect(screen.getByText(/Invoice details/)).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('AED')).toBeInTheDocument();
    expect(screen.getByText('Item name 1')).toBeInTheDocument();
    expect(screen.getByText('Item name 2')).toBeInTheDocument();
    expect(screen.getByText('AED 200.00')).toBeInTheDocument();
  });

  it('shows print invoice button', () => {
    render(<InvoiceDetailsPage invoice={mockInvoice} />);

    const printButton = screen.getByText('Print Invoice');
    expect(printButton).toBeInTheDocument();
  });

  it('shows action buttons', () => {
    render(<InvoiceDetailsPage invoice={mockInvoice} />);

    expect(screen.getByText('Resend')).toBeInTheDocument();
    expect(screen.getByText('Duplicate')).toBeInTheDocument();
  });

  it('shows payment URL when available', () => {
    render(<InvoiceDetailsPage invoice={mockInvoice} />);

    expect(screen.getByText(mockInvoice.payment_url!)).toBeInTheDocument();
    expect(screen.getByText('Copy')).toBeInTheDocument();
  });

  it('shows breadcrumb navigation', () => {
    render(<InvoiceDetailsPage invoice={mockInvoice} />);

    expect(screen.getByText(/Dashboard.*POS.*Invoicing.*#3213/)).toBeInTheDocument();
  });

  it('handles print button click', () => {
    const printSpy = vi.spyOn(window, 'print').mockImplementation(() => {});
    
    render(<InvoiceDetailsPage invoice={mockInvoice} />);

    const printButton = screen.getByText('Print Invoice');
    fireEvent.click(printButton);

    expect(printSpy).toHaveBeenCalled();
    
    printSpy.mockRestore();
  });

  it('shows back link to invoices', () => {
    render(<InvoiceDetailsPage invoice={mockInvoice} />);

    const backLink = screen.getByTestId('link');
    expect(backLink).toHaveAttribute('href', '/dashboard/pos/invoicing');
  });

  it('handles missing payment URL gracefully', () => {
    const invoiceWithoutUrl = { ...mockInvoice, payment_url: undefined };
    render(<InvoiceDetailsPage invoice={invoiceWithoutUrl} />);

    expect(screen.getByText('Link unavailable, try again later')).toBeInTheDocument();
  });

  it('handles resend button click for enabled invoices', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

    render(<InvoiceDetailsPage invoice={mockInvoice} />);

    const resendButton = screen.getByText('Resend').closest('button');
    fireEvent.click(resendButton!);

    expect(consoleSpy).toHaveBeenCalledWith('Resending invoice:', mockInvoice.id);
    consoleSpy.mockRestore();
  });

  it('handles duplicate button click', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

    render(<InvoiceDetailsPage invoice={mockInvoice} />);

    const duplicateButton = screen.getByText('Duplicate').closest('button');
    fireEvent.click(duplicateButton!);

    expect(consoleSpy).toHaveBeenCalledWith('Duplicating invoice:', mockInvoice.id);
    consoleSpy.mockRestore();
  });

  it('does not handle resend for draft invoices', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

    const draftInvoice = { ...mockInvoice, status: 'DRAFT' as const };
    render(<InvoiceDetailsPage invoice={draftInvoice} />);

    const resendButton = screen.getByText('Resend').closest('button');
    fireEvent.click(resendButton!);

    expect(consoleSpy).not.toHaveBeenCalled();
    consoleSpy.mockRestore();
  });

});
