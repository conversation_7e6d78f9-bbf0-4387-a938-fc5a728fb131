import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Invoice } from '@/features/dashboard/types';
import { getInvoiceStatus, getInvoiceStatusColor } from '@/lib/constants/status';
import { cn } from '@/lib/utils';
import { formatFiatAmount } from '@/lib/utils/currency';
import { formatDate } from 'date-fns';
import { CopyButton } from '@/components/ui/copy-button';
import { useEffect, useState } from 'react';
import { toast } from '@/hooks/use-toast';
import { hasIncompleteFields } from '@/lib/utils/stringUtils';
import { AlertTriangle, RefreshCw, ArrowLeft, Printer } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Page } from '@/components/layouts/Page';
import { Link } from '@tanstack/react-router';

interface InvoiceDetailsPageProps {
  invoice: Invoice;
}

export function InvoiceDetailsPage({ invoice }: InvoiceDetailsPageProps) {
  const [showSyncBanner, setShowSyncBanner] = useState(false);

  useEffect(() => {
    if (invoice && hasIncompleteFields(invoice)) {
      setShowSyncBanner(true);
    } else {
      setShowSyncBanner(false);
    }
  }, [invoice]);

  const isDraft = invoice.status === 'DRAFT';
  const hasPaymentUrl = Boolean(invoice.payment_url);
  const statusText = getInvoiceStatus(invoice.status);
  const statusColor = getInvoiceStatusColor(invoice.status);

  const handlePrint = () => {
    window.print();
  };

  const handleResend = () => {
    if (isDraft) return;
    toast({
      title: 'Invoice Resent',
      description: `Invoice ${invoice.invoice_id} has been resent to ${invoice.email}`,
    });
  };

  const handleDuplicate = () => {
    toast({
      title: 'Invoice Duplicated',
      description: `A copy of invoice ${invoice.invoice_id} has been created`,
    });
  };

  const handleRefresh = () => {
    setShowSyncBanner(false);
    toast({
      title: 'Status Refreshed',
      description: 'Invoice status has been updated',
    });
  };

  return (
    <Page
      title={
        <div className="flex items-center gap-3">
          <Link
            to="/dashboard/pos/invoicing"
            className="text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <span>Invoice details - #{invoice.invoice_id}</span>
          <Badge className={cn('text-white', statusColor)}>{statusText}</Badge>
        </div>
      }
      actions={
        <Button variant="outline" onClick={handlePrint} className="gap-2">
          <Printer className="h-4 w-4" />
          Print Invoice
        </Button>
      }
    >
      <div className="max-w-4xl mx-auto">
        {/* Breadcrumb */}
        <div className="text-sm text-muted-foreground mb-6">
          Dashboard &gt; POS &gt; Invoicing &gt; #{invoice.invoice_id}
        </div>

        {showSyncBanner && (
          <Alert className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>Status may be outdated. Refresh?</span>
              <Button variant="ghost" size="sm" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-6">
            <div>
              <Label className="text-muted-foreground">Created</Label>
              <p className="text-foreground">
                {formatDate(new Date(invoice.created_at), 'dd-MM-yyyy HH:mm')}
              </p>
            </div>
            <div>
              <Label className="text-muted-foreground">Due date</Label>
              <p className="text-foreground">
                {formatDate(new Date(invoice.due_date), 'dd-MM-yyyy')}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-muted-foreground">Email address</Label>
            <p className="text-foreground">{invoice.email}</p>
            {invoice.cc_emails && invoice.cc_emails.length > 0 && (
              <p className="text-foreground">{invoice.cc_emails.join(', ')}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label className="text-muted-foreground">Payment URL</Label>
            {hasPaymentUrl ? (
              <div className="flex items-center gap-2">
                <span className="font-mono text-sm bg-muted p-2 rounded flex-1 truncate text-foreground">
                  {invoice.payment_url}
                </span>
                <CopyButton value={invoice.payment_url!} variant="outline" size="sm">
                  Copy
                </CopyButton>
              </div>
            ) : (
              <p className="text-muted-foreground">Link unavailable, try again later</p>
            )}
          </div>

          <div className="space-y-4">
            <Label className="text-muted-foreground">Order details</Label>
            <div className="bg-card rounded-lg border p-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-muted-foreground">Currency</Label>
                  <p className="text-foreground">{invoice.currency}</p>
                </div>

                <div className="space-y-3">
                  <div className="grid grid-cols-4 gap-4 text-sm font-medium text-muted-foreground">
                    <span>Item</span>
                    <span className="text-right">Price</span>
                    <span className="text-right">Qty</span>
                    <span className="text-right">Amount</span>
                  </div>
                  {invoice.items.map((item) => (
                    <div key={item.id} className="grid grid-cols-4 gap-4 text-sm text-foreground">
                      <span>{item.name}</span>
                      <span className="text-right">
                        {formatFiatAmount(item.price, invoice.currency)}
                      </span>
                      <span className="text-right">{item.quantity}</span>
                      <span className="text-right">
                        {formatFiatAmount(item.amount, invoice.currency)}
                      </span>
                    </div>
                  ))}
                  <div className="border-t pt-3 mt-3">
                    <div className="grid grid-cols-4 gap-4 text-sm font-medium">
                      <span className="col-span-3 text-right text-foreground">Order total</span>
                      <span className="text-right text-foreground">
                        {formatFiatAmount(invoice.total, invoice.currency)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex gap-3">
            <Button variant="outline" onClick={handleResend} disabled={isDraft}>
              Resend
            </Button>
            <Button variant="outline" onClick={handleDuplicate}>
              Duplicate
            </Button>
          </div>
        </div>
      </div>
    </Page>
  );
}
