import { Badge } from '@/components/ui/badge';
import { Invoice } from '@/features/dashboard/types';
import { getInvoiceStatus, getInvoiceStatusColor } from '@/lib/constants/status';
import { cn } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';
import { FilterFn } from '@tanstack/react-table';
import { format } from 'date-fns';
import { formatFiatAmount } from '@/lib/utils/currency';

const multiColumnFilterFn: FilterFn<Invoice> = (row, _, filterValue) => {
  const searchableRowContent = `${row.original.invoice_id} ${row.original.email} ${row.original.total} ${row.original.status}`;

  return searchableRowContent.toLowerCase().includes(filterValue.toLowerCase().trim());
};

export const InvoiceColumns: ColumnDef<Invoice>[] = [
  {
    accessorKey: 'invoice_id',
    header: 'ID',
    cell: ({ row }) => {
      return (
        <div className="font-medium text-white">
          #{row.original.invoice_id}
        </div>
      );
    },
    filterFn: multiColumnFilterFn,
  },
  {
    accessorKey: 'created_at',
    header: 'Created',
    cell: ({ row }) => {
      const date = new Date(row.original.created_at);
      return (
        <div className="text-white">
          {format(date, 'dd-MM-yyyy')}
        </div>
      );
    },
  },
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => {
      return (
        <div className="text-white">
          {row.original.email}
        </div>
      );
    },
  },
  {
    accessorKey: 'total',
    header: () => <div className="text-right">Total</div>,
    cell: ({ row }) => {
      const total = row.original.total;
      const currency = row.original.currency;
      const formatted = formatFiatAmount(total, currency);

      return (
        <div className="font-medium text-right text-white">
          {formatted}
        </div>
      );
    },
  },
  {
    accessorKey: 'due_date',
    header: 'Due Date',
    cell: ({ row }) => {
      const date = new Date(row.original.due_date);
      const now = new Date();
      const isOverdue = date < now && row.original.status !== 'PAID';

      return (
        <div className={cn('text-white', isOverdue && 'text-destructive')}>
          {format(date, 'dd-MM-yyyy')}
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      const statusText = getInvoiceStatus(status);
      const statusColor = getInvoiceStatusColor(status);

      return (
        <Badge className={cn('text-white', statusColor)}>
          {statusText}
        </Badge>
      );
    },
  },
];
