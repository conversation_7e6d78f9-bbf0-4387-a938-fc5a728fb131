import { Badge } from '@/components/ui/badge';
import { Invoice } from '@/features/dashboard/types';
import { getInvoiceStatus, getInvoiceStatusColor } from '@/lib/constants/status';
import { cn } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';
import { FilterFn } from '@tanstack/react-table';
import { format } from 'date-fns';
import { formatFiatAmount } from '@/lib/utils/currency';

const multiColumnFilterFn: FilterFn<Invoice> = (row, _, filterValue) => {
  const searchableRowContent = `${row.original.invoice_id} ${row.original.email} ${row.original.total} ${row.original.status}`;

  return searchableRowContent.toLowerCase().includes(filterValue.toLowerCase().trim());
};

export const InvoiceColumns: ColumnDef<Invoice>[] = [
  {
    accessorKey: 'invoice_id',
    header: 'ID',
    cell: ({ row }) => {
      return (
        <div className="font-medium">
          {row.original.invoice_id}
        </div>
      );
    },
    filterFn: multiColumnFilterFn,
  },
  {
    accessorKey: 'created_at',
    header: 'Created',
    cell: ({ row }) => {
      const date = new Date(row.original.created_at);
      return (
        <div>
          <p>{format(date, 'dd-MM-yyyy')}</p>
          <p className="text-card-foreground text-sm">{format(date, 'HH:mm')}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => {
      return (
        <div>
          <p className="text-foreground">{row.original.email}</p>
          {row.original.cc_emails && row.original.cc_emails.length > 0 && (
            <p className="text-card-foreground text-sm">
              CC: {row.original.cc_emails.join(', ')}
            </p>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'total',
    header: () => <div className="text-right">Total</div>,
    cell: ({ row }) => {
      const total = row.original.total;
      const currency = row.original.currency;
      const formatted = formatFiatAmount(total, currency);

      return (
        <div className="font-medium text-right">
          <p className="text-foreground">{formatted}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'due_date',
    header: 'Due Date',
    cell: ({ row }) => {
      const date = new Date(row.original.due_date);
      const now = new Date();
      const isOverdue = date < now && row.original.status !== 'PAID';
      
      return (
        <div>
          <p className={cn(isOverdue && 'text-destructive')}>
            {format(date, 'dd-MM-yyyy')}
          </p>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      const statusText = getInvoiceStatus(status);
      const statusColor = getInvoiceStatusColor(status);

      return (
        <Badge className={cn('text-white', statusColor)}>
          {statusText}
        </Badge>
      );
    },
  },
];
