import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { InvoiceDetailsDialog } from './InvoiceDetailsDialog';
import { Invoice } from '@/features/dashboard/types';

vi.mock('@/hooks/use-toast', () => ({
  toast: vi.fn(),
}));

vi.mock('@/components/ui/copy-button', () => ({
  CopyButton: ({ value, children }: { value: string; children: React.ReactNode }) => (
    <button data-testid="copy-button" data-value={value}>
      {children}
    </button>
  ),
}));

const mockInvoice: Invoice = {
  id: '1',
  invoice_id: '#3213',
  status: 'PENDING',
  email: '<EMAIL>',
  cc_emails: ['<EMAIL>'],
  items: [
    {
      id: '1',
      name: 'Item name 1',
      price: 100,
      quantity: 1,
      amount: 100,
    },
    {
      id: '2',
      name: 'Item name 2',
      price: 100,
      quantity: 1,
      amount: 100,
    },
  ],
  total: 200.0,
  currency: 'AED',
  payment_url: 'https://checkout.wefi.solutions/email-order/f926a29a-d5ef-4af7-a0e6-f31',
  created_at: Date.now() - 86400000,
  due_date: Date.now() + 86400000,
};

const mockDraftInvoice: Invoice = {
  ...mockInvoice,
  id: '2',
  invoice_id: '#3214',
  status: 'DRAFT',
  payment_url: undefined,
  due_date: Date.now() - 86400000,
};

describe('InvoiceDetailsDialog', () => {
  it('renders invoice details correctly', () => {
    render(
      <InvoiceDetailsDialog
        invoice={mockInvoice}
        open={true}
        onOpenChange={vi.fn()}
      />
    );

    expect(screen.getByText('Invoice details - #3213')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Item name 1')).toBeInTheDocument();
    expect(screen.getByText('Item name 2')).toBeInTheDocument();
    expect(screen.getByText('Pending payment')).toBeInTheDocument();
  });

  it('shows payment URL with copy button when available', () => {
    render(
      <InvoiceDetailsDialog
        invoice={mockInvoice}
        open={true}
        onOpenChange={vi.fn()}
      />
    );

    expect(screen.getByText(mockInvoice.payment_url!)).toBeInTheDocument();
    expect(screen.getByTestId('copy-button')).toBeInTheDocument();
  });

  it('shows fallback message when payment URL is missing', () => {
    render(
      <InvoiceDetailsDialog
        invoice={mockDraftInvoice}
        open={true}
        onOpenChange={vi.fn()}
      />
    );

    expect(screen.getByText('Link unavailable, try again later')).toBeInTheDocument();
    expect(screen.queryByTestId('copy-button')).not.toBeInTheDocument();
  });

  it('disables resend button for draft invoices', () => {
    render(
      <InvoiceDetailsDialog
        invoice={mockDraftInvoice}
        open={true}
        onOpenChange={vi.fn()}
      />
    );

    const resendButton = screen.getByText('Resend').closest('button');
    expect(resendButton).toBeDisabled();
  });

  it('enables resend button for non-expired invoices', () => {
    render(
      <InvoiceDetailsDialog
        invoice={mockInvoice}
        open={true}
        onOpenChange={vi.fn()}
      />
    );

    const resendButton = screen.getByText('Resend').closest('button');
    expect(resendButton).not.toBeDisabled();
  });

  it('calls onOpenChange when dialog is closed', () => {
    const onOpenChange = vi.fn();
    render(
      <InvoiceDetailsDialog
        invoice={mockInvoice}
        open={true}
        onOpenChange={onOpenChange}
      />
    );

    fireEvent.keyDown(document, { key: 'Escape' });
   
  });

  it('does not render when invoice is null', () => {
    const { container } = render(
      <InvoiceDetailsDialog
        invoice={null}
        open={true}
        onOpenChange={vi.fn()}
      />
    );

    expect(container.firstChild).toBeNull();
  });
});
