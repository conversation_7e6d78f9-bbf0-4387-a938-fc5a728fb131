import { Search } from '@/assets/icons';
import { Input } from '@/components/ui/input';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useState, useEffect } from 'react';
import { useRouter } from '@tanstack/react-router';
import { LoadingState } from '../LoadingState';
import { Invoice } from '@/features/dashboard/types';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useInvoicesQuery } from '@/features/dashboard/hooks/useInvoicesQuery';

interface InvoicesTableProps {
  columns: ColumnDef<Invoice>[];
}

export function InvoicesTable({ columns }: InvoicesTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [searchValue, setSearchValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const router = useRouter();

  // Fetch data with current parameters
  const { data: invoicesResponse, isLoading, isError } = useInvoicesQuery({
    page: currentPage,
    limit: 10,
    search: searchValue,
  });

  // Update search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentPage(1); // Reset to first page when searching
    }, 300);

    return () => clearTimeout(timer);
  }, [searchValue]);

  const table = useReactTable({
    data: invoicesResponse?.data ?? [],
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    manualPagination: true,
    pageCount: invoicesResponse?.totalPages ?? 0,
  });

  const handleRowClick = (invoice: Invoice) => {
    router.navigate({
      to: '/dashboard/pos/invoicing/$invoiceId',
      params: { invoiceId: invoice.invoice_id }
    });
  };

  if (isLoading) {
    return <LoadingState />;
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-destructive">Failed to load invoices. Please try again.</p>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      <div className="relative max-w-sm">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search: email, status, total"
          value={searchValue}
          onChange={(event) => setSearchValue(event.target.value)}
          className="pl-10 bg-[#2A2A2A] border-[#3A3A3A] text-white placeholder:text-gray-400"
        />
      </div>

      <div className="bg-[#1A1A1A] rounded-lg">
        <div className="grid grid-cols-6 gap-4 px-4 py-3 text-sm font-medium text-gray-400">
          <div>ID</div>
          <div>CREATED</div>
          <div>EMAIL</div>
          <div className="text-right">TOTAL</div>
          <div>DUE DATE</div>
          <div>STATUS</div>
        </div>

        <div className="space-y-1 p-1">
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <div
                key={row.id}
                className="grid grid-cols-6 gap-4 px-4 py-4 bg-[#2A2A2A] rounded-md cursor-pointer hover:bg-[#333333] transition-colors"
                onClick={() => handleRowClick(row.original as Invoice)}
              >
                {row.getVisibleCells().map((cell, index) => (
                  <div key={cell.id} className={index === 3 ? 'text-right' : ''}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </div>
                ))}
              </div>
            ))
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <h3 className="text-lg font-medium">No invoices found</h3>
                <p className="text-muted-foreground">There are no invoices matching your search criteria.</p>
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center justify-between px-4 py-4">
          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="w-8 h-8 rounded-[5px] bg-[#2A2A2A] hover:bg-[#333333] disabled:opacity-50 disabled:cursor-not-allowed text-gray-400 flex items-center justify-center"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, invoicesResponse?.totalPages ?? 0) }, (_, i) => {
                const pageNumber = i + 1;
                const isActive = pageNumber === currentPage;
                return (
                  <button
                    key={i}
                    onClick={() => setCurrentPage(pageNumber)}
                    className={`w-8 h-8 rounded-[5px] text-sm font-medium flex items-center justify-center ${
                      isActive
                        ? 'bg-primary text-black'
                        : 'bg-[#2A2A2A] hover:bg-[#333333] text-white'
                    }`}
                  >
                    {pageNumber}
                  </button>
                );
              })}
              {(invoicesResponse?.totalPages ?? 0) > 5 && (
                <>
                  <span className="px-2 text-gray-400">...</span>
                  <button
                    onClick={() => setCurrentPage(invoicesResponse?.totalPages ?? 1)}
                    className="w-8 h-8 rounded-[5px] text-sm font-medium bg-[#2A2A2A] hover:bg-[#333333] text-white flex items-center justify-center"
                  >
                    {invoicesResponse?.totalPages}
                  </button>
                </>
              )}
            </div>

            <button
              onClick={() => setCurrentPage(prev => Math.min(invoicesResponse?.totalPages ?? 1, prev + 1))}
              disabled={currentPage === (invoicesResponse?.totalPages ?? 1)}
              className="w-8 h-8 rounded-[5px] bg-[#2A2A2A] hover:bg-[#333333] disabled:opacity-50 disabled:cursor-not-allowed text-gray-400 flex items-center justify-center"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>

          <div className="text-sm text-gray-400">
            PAGE {currentPage} OF {invoicesResponse?.totalPages ?? 1}
          </div>
        </div>
      </div>
    </div>
  );
}
