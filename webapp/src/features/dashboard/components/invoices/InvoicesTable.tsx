import { Search } from '@/assets/icons';
import { Page } from '@/components/layouts/Page';
import { Input } from '@/components/ui/input';
import { DataTablePagination } from '@/components/ui/table-pagination';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useState } from 'react';
import { useRouter } from '@tanstack/react-router';
import { LoadingState } from '../LoadingState';
import { Invoice } from '@/features/dashboard/types';

interface InvoicesTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  title: string;
  isLoading?: boolean;
  isError?: boolean;
}

export function InvoicesTable<TData, TValue>({
  columns,
  data,
  title,
  isLoading = false,
  isError = false,
}: InvoicesTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const router = useRouter();

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const handleRowClick = (invoice: Invoice) => {
    router.navigate({
      to: '/dashboard/pos/invoicing/$invoiceId',
      params: { invoiceId: invoice.invoice_id }
    });
  };

  if (isLoading) {
    return <LoadingState />;
  }

  if (isError) {
    return (
      <Page title={title}>
        <div className="flex items-center justify-center h-64">
          <p className="text-destructive">Failed to load invoices. Please try again.</p>
        </div>
      </Page>
    );
  }

  return (
    <Page title={title}>
      <div className="w-full">
        <div className="flex items-center justify-between py-4">
          <h1 className="text-2xl font-semibold">{title}</h1>
        </div>
        <div className="flex items-center py-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search email, status, total..."
              value={(table.getColumn('invoice_id')?.getFilterValue() as string) ?? ''}
              onChange={(event) =>
                table.getColumn('invoice_id')?.setFilterValue(event.target.value)
              }
              className="max-w-sm pl-10"
            />
          </div>
        </div>
        <div className="space-y-3">
          {/* Table Header */}
          <div className="grid grid-cols-6 gap-4 px-4 py-3 text-sm font-medium text-muted-foreground border-b">
            <div>ID</div>
            <div>CREATED</div>
            <div>EMAIL</div>
            <div className="text-right">TOTAL</div>
            <div>DUE DATE</div>
            <div>STATUS</div>
          </div>

          {/* Table Rows */}
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <div
                key={row.id}
                className="grid grid-cols-6 gap-4 px-4 py-4 bg-card rounded-lg border cursor-pointer hover:bg-accent/50 transition-colors"
                onClick={() => handleRowClick(row.original as Invoice)}
              >
                {row.getVisibleCells().map((cell, index) => (
                  <div key={cell.id} className={index === 3 ? 'text-right' : ''}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </div>
                ))}
              </div>
            ))
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <h3 className="text-lg font-medium">No invoices found</h3>
                <p className="text-muted-foreground">There are no invoices matching your search criteria.</p>
              </div>
            </div>
          )}
        </div>
        <DataTablePagination table={table} />
      </div>
    </Page>
  );
}
