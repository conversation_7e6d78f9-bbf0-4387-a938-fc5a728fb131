import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { PaymentDetailsDialog } from './PaymentDetailsDialog';

const mockPayment = {
  id: '1',
  amount_fiat: 100,
  currency_fiat: 'USD',
  status: 'PAID' as const,
  created_at: Date.now(),
  invoice_id: 'INV-001',
  email: '<EMAIL>',
};

describe('PaymentDetailsDialog', () => {
  it('renders payment details correctly when open', () => {
    render(
      <PaymentDetailsDialog
        payment={mockPayment}
        isOpen={true}
        onClose={vi.fn()}
      />
    );

    expect(screen.getByText('Transaction Details')).toBeInTheDocument();
    expect(screen.getByText('$100.00')).toBeInTheDocument();
    expect(screen.getByText('Completed')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(
      <PaymentDetailsDialog
        payment={mockPayment}
        isOpen={false}
        onClose={vi.fn()}
      />
    );

    expect(screen.queryByText('Transaction Details')).not.toBeInTheDocument();
  });

  it('handles empty payment object', () => {
    render(
      <PaymentDetailsDialog
        payment={{}}
        isOpen={true}
        onClose={vi.fn()}
      />
    );

    expect(screen.getByText('Transaction Details')).toBeInTheDocument();
  });

  it('calls onClose when dialog state changes', () => {
    const onClose = vi.fn();

    render(
      <PaymentDetailsDialog
        payment={mockPayment}
        isOpen={true}
        onClose={onClose}
      />
    );

    // Dialog should be visible
    expect(screen.getByText('Transaction Details')).toBeInTheDocument();
  });
});
