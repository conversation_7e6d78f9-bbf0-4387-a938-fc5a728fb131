import { useQuery } from '@tanstack/react-query';
import { Invoice } from '@/features/dashboard/types';

async function fetchInvoices(): Promise<Invoice[]> {
  // TODO: Replace with actual API call
  const mockData: Invoice[] = [
    {
      id: '1',
      invoice_id: '#3213',
      status: 'PENDING',
      email: '<EMAIL>',
      cc_emails: ['<EMAIL>'],
      items: [
        {
          id: '1',
          name: 'Item name 1',
          price: 100,
          quantity: 1,
          amount: 100,
        },
        {
          id: '2',
          name: 'Item name 2',
          price: 100,
          quantity: 1,
          amount: 100,
        },
      ],
      total: 200.0,
      currency: 'AED',
      payment_url: 'https://checkout.wefi.solutions/email-order/f926a29a-d5ef-4af7-a0e6-f31',
      created_at: Date.now() - 86400000, // 1 day ago
      due_date: Date.now() + 86400000, // 1 day from now
    },
    {
      id: '2',
      invoice_id: '#3214',
      status: 'PAID',
      email: '<EMAIL>',
      items: [
        {
          id: '1',
          name: 'Service Payment',
          price: 500,
          quantity: 1,
          amount: 500,
        },
      ],
      total: 500.0,
      currency: 'AED',
      payment_url: 'https://checkout.wefi.solutions/email-order/f926a29a-d5ef-4af7-a0e6-f32',
      created_at: Date.now() - 172800000, // 2 days ago
      due_date: Date.now() - 86400000, // 1 day ago
      processed_at: Date.now() - 86400000,
    },
    {
      id: '3',
      invoice_id: '#3215',
      status: 'EXPIRED',
      email: '<EMAIL>',
      items: [
        {
          id: '1',
          name: 'Product Purchase',
          price: 1500,
          quantity: 1,
          amount: 1500,
        },
      ],
      total: 1500.0,
      currency: 'AED',
      created_at: Date.now() - 259200000, // 3 days ago
      due_date: Date.now() - 172800000, // 2 days ago (expired)
    },
    {
      id: '4',
      invoice_id: '#3216',
      status: 'PENDING',
      email: '<EMAIL>',
      cc_emails: ['<EMAIL>'],
      items: [
        {
          id: '1',
          name: 'Consultation Service',
          price: 750,
          quantity: 2,
          amount: 1500,
        },
      ],
      total: 1500.0,
      currency: 'AED',
      payment_url: 'https://checkout.wefi.solutions/email-order/f926a29a-d5ef-4af7-a0e6-f33',
      created_at: Date.now() - 345600000, // 4 days ago
      due_date: Date.now() + 172800000, // 2 days from now
    },
    {
      id: '5',
      invoice_id: '#3217',
      status: 'PAID',
      email: '<EMAIL>',
      items: [
        {
          id: '1',
          name: 'Monthly Subscription',
          price: 299,
          quantity: 1,
          amount: 299,
        },
      ],
      total: 299.0,
      currency: 'AED',
      payment_url: 'https://checkout.wefi.solutions/email-order/f926a29a-d5ef-4af7-a0e6-f34',
      created_at: Date.now() - 432000000, // 5 days ago
      due_date: Date.now() - 259200000, // 3 days ago
      processed_at: Date.now() - 259200000,
    },
  ];

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 800));
  return mockData;
}

export function useInvoicesQuery() {
  return useQuery({
    queryKey: ['invoices'],
    queryFn: fetchInvoices,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
