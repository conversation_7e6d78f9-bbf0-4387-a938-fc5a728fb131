import { useQuery } from '@tanstack/react-query';
import { Invoice } from '@/features/dashboard/types';

interface InvoicesQueryParams {
  page?: number;
  limit?: number;
  search?: string;
}

interface InvoicesResponse {
  data: Invoice[];
  total: number;
  page: number;
  totalPages: number;
}

async function fetchInvoices(params: InvoicesQueryParams = {}): Promise<InvoicesResponse> {
  const { page = 1, limit = 10, search = '' } = params;

  // Generate more mock data for pagination
  const generateMockInvoices = (count: number): Invoice[] => {
    const statuses: ('PENDING' | 'PAID' | 'DRAFT')[] = ['PENDING', 'PAID', 'DRAFT'];
    const emails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    return Array.from({ length: count }, (_, i) => {
      const id = (i + 1).toString();
      const invoiceNumber = 3213 + i;
      const status = statuses[i % statuses.length];
      const email = emails[i % emails.length];
      const amount = Math.floor(Math.random() * 2000) + 100;
      const daysAgo = Math.floor(Math.random() * 30);
      const daysUntilDue = status === 'PAID' ? -Math.floor(Math.random() * 10) : Math.floor(Math.random() * 30) + 1;

      return {
        id,
        invoice_id: `#${invoiceNumber}`,
        status,
        email,
        cc_emails: status === 'PENDING' ? [email] : undefined,
        items: [
          {
            id: '1',
            name: `Service ${i + 1}`,
            price: amount,
            quantity: 1,
            amount,
          },
        ],
        total: amount,
        currency: 'AED',
        payment_url: `https://checkout.wefi.solutions/email-order/f926a29a-d5ef-4af7-a0e6-f${i}`,
        created_at: Date.now() - (daysAgo * 86400000),
        due_date: Date.now() + (daysUntilDue * 86400000),
        processed_at: status === 'PAID' ? Date.now() - (Math.floor(Math.random() * 10) * 86400000) : undefined,
      };
    });
  };

  const allInvoices = generateMockInvoices(52); // Generate 52 invoices for pagination

  // Filter by search if provided
  let filteredInvoices = allInvoices;
  if (search) {
    const searchLower = search.toLowerCase();
    filteredInvoices = allInvoices.filter(invoice =>
      invoice.invoice_id.toLowerCase().includes(searchLower) ||
      invoice.email.toLowerCase().includes(searchLower) ||
      invoice.status.toLowerCase().includes(searchLower) ||
      invoice.total.toString().includes(searchLower)
    );
  }

  // Calculate pagination
  const total = filteredInvoices.length;
  const totalPages = Math.ceil(total / limit);
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedData = filteredInvoices.slice(startIndex, endIndex);

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  return {
    data: paginatedData,
    total,
    page,
    totalPages,
  };
}

export function useInvoicesQuery(params: InvoicesQueryParams = {}) {
  return useQuery({
    queryKey: ['invoices', params],
    queryFn: () => fetchInvoices(params),
    staleTime: 5 * 60 * 1000,
  });
}

// Function to get a single invoice by ID
async function fetchInvoiceById(invoiceId: string): Promise<Invoice | null> {
  // Generate the same mock data to find the invoice
  const generateMockInvoices = (count: number): Invoice[] => {
    const statuses: ('PENDING' | 'PAID' | 'DRAFT')[] = ['PENDING', 'PAID', 'DRAFT'];
    const emails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    return Array.from({ length: count }, (_, i) => {
      const id = (i + 1).toString();
      const invoiceNumber = 3213 + i;
      const status = statuses[i % statuses.length];
      const email = emails[i % emails.length];
      const amount = Math.floor(Math.random() * 2000) + 100;
      const daysAgo = Math.floor(Math.random() * 30);
      const daysUntilDue = status === 'PAID' ? -Math.floor(Math.random() * 10) : Math.floor(Math.random() * 30) + 1;

      return {
        id,
        invoice_id: `#${invoiceNumber}`,
        status,
        email,
        cc_emails: status === 'PENDING' ? [email] : undefined,
        items: [
          {
            id: '1',
            name: `Service ${i + 1}`,
            price: amount,
            quantity: 1,
            amount,
          },
        ],
        total: amount,
        currency: 'AED',
        payment_url: `https://checkout.wefi.solutions/email-order/f926a29a-d5ef-4af7-a0e6-f${i}`,
        created_at: Date.now() - (daysAgo * 86400000),
        due_date: Date.now() + (daysUntilDue * 86400000),
        processed_at: status === 'PAID' ? Date.now() - (Math.floor(Math.random() * 10) * 86400000) : undefined,
      };
    });
  };

  const allInvoices = generateMockInvoices(52);

  // Find invoice by invoice_id (like #3213)
  const invoice = allInvoices.find(inv => inv.invoice_id === invoiceId);

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  return invoice || null;
}

export function useInvoiceQuery(invoiceId: string) {
  return useQuery({
    queryKey: ['invoice', invoiceId],
    queryFn: () => fetchInvoiceById(invoiceId),
    staleTime: 5 * 60 * 1000,
    enabled: !!invoiceId,
  });
}
