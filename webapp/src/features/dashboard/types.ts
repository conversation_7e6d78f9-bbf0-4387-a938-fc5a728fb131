export type PaymentMethod = 'POS' | 'QR' | 'Invoice' | 'API';

export type PaymentStatus =
  | 'PAID'
  | 'PENDING'
  | 'EXPIRED'
  | 'CANCELED'
  | 'UNDERPAID'
  | 'REFUNDED'
  | 'FAILED'
  | 'BLOCKED';

export type InvoiceStatus = 'PAID' | 'PENDING' | 'DRAFT';

export interface Payment {
  id: string;
  address: string;
  amount: number;
  date: string;
  payment_method: PaymentMethod;
  coin?: {
    coin_id: string;
    name_short: string;
  };
  application?: {
    name: string;
  };
  created_at?: number;
  processed_at?: number;
  amount_fiat?: number;
  currency_fiat?: string;
  status?: PaymentStatus;
}

export interface BalanceItem {
  id: string;
  name: string;
  currency: string;
  coin_id: string;
  amount: number;
}

export type PosMethod = 'DEVICE' | 'QR' | 'INVOICE' | 'API' | 'ECOMMERCE';

export interface POSItem {
  id: string;
  name: string;
  amount: number;
  type: PosMethod;
  paired?: number;
  unpaired?: number;
  active?: number;
  connected?: number;
  pending?: number;
  total?: number;
}

// Pos devices types
export type PosDeviceType = 'Mobile' | 'Terminal';

// Pos devices status types
export type PosDeviceStatus = 'PAIRED' | 'UNPAIRED';

export interface POSDevice {
  id: string;
  businessName: string;
  address: string;
  country?: string;
  status: PosDeviceStatus;
  dateAdded: number;
  type: PosDeviceType;
}

// Invoice item interface
export interface InvoiceItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  amount: number;
}

// Invoice interface
export interface Invoice {
  id: string;
  invoice_id: string; // Display ID like #3213
  status: InvoiceStatus;
  email: string;
  cc_emails?: string[];
  items: InvoiceItem[];
  total: number;
  currency: string;
  payment_url?: string;
  created_at: number;
  due_date: number;
  processed_at?: number;
}

export interface DashboardData {
  paymentsData: Payment[];
  balanceData: BalanceItem[];
  posData: POSItem[];
}
