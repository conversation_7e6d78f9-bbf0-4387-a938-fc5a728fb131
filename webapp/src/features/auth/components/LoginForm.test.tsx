import { describe, expect, it, vi, beforeEach, expectTypeOf } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { LoginForm, LoginFormProps } from './LoginForm';
import * as formHook from '@/components/forms/Form';
import { store } from '@/store';
import { useNavigate } from '@tanstack/react-router';
import z from 'zod/v4';

interface FieldProps {
  label: string;
  type: string;
}

interface AppFieldProps {
  children: (field: { TextField: (props: FieldProps) => JSX.Element }) => JSX.Element;
  name: string;
}

interface FormConfig {
  validators?: {
    onChange?: {
      parse: (values: { email: string; password: string }) => void;
    };
  };
  onSubmit?: (data: { value: { email: string; password: string } }) => void;
}

// Create a proper mock store
const mockStore = {
  dispatch: vi.fn(),
  getState: vi.fn(() => ({
    auth: {
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      currentStep: 'sign-in',
      signUpEmail: null,
    },
  })),
  subscribe: vi.fn(),
  replaceReducer: vi.fn(),
};

vi.mock('@/store', () => ({
  store: mockStore,
}));

vi.mock('@tanstack/react-router', () => ({
  useNavigate: vi.fn(),
}));

// Helper function to render with Redux Provider
function renderWithProvider(component: React.ReactElement) {
  return render(
    <Provider store={mockStore as any}>
      {component}
    </Provider>
  );
}

// Create a mock TextField component that matches TanStack's pattern
function TextField({ label, type }: FieldProps) {
  return (
    <div>
      <label>{label}</label>
      <input type={type} aria-label={label} data-testid={`input-${label.toLowerCase()}`} />
    </div>
  );
}

vi.mock('@/components/forms/Form', () => ({
  useAppForm: vi.fn((config: FormConfig) => ({
    AppField: ({ children }: AppFieldProps) => {
      const field = {
        TextField,
      };
      return children(field);
    },
    handleSubmit: () => {
      config.onSubmit?.({
        value: {
          email: '<EMAIL>',
          password: 'password123',
        },
      });
    },
  })),
}));

describe('LoginForm', () => {
  const mockNavigate = vi.fn();
  const mockOnSuccess = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useNavigate as any).mockReturnValue(mockNavigate);
  });

  it('should render form fields correctly', () => {
    renderWithProvider(<LoginForm />);

    expect(screen.getByTestId('login-form')).toBeInTheDocument();
    expect(screen.getByLabelText('Email address')).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /continue/i })).toBeInTheDocument();
  });

  it('should handle successful form submission', async () => {
    renderWithProvider(<LoginForm onSuccess={mockOnSuccess} />);

    await userEvent.click(screen.getByRole('button', { name: /continue/i }));

    await waitFor(() => {
      expect(mockStore.dispatch).toHaveBeenCalled();
    });

    expect(mockNavigate).toHaveBeenCalledWith({ to: '/dashboard' });
    expect(mockOnSuccess).toHaveBeenCalled();
  });

  it('should validate form fields', async () => {
    const { useAppForm } = formHook;
    const mockValidate = vi.fn();

    (useAppForm as any).mockImplementation((config: FormConfig) => ({
      AppField: ({ children, name }: AppFieldProps) => {
        const field = {
          TextField: ({ label, type }: FieldProps) => (
            <input
              type={type}
              aria-label={label}
              onChange={(e) => {
                try {
                  config.validators?.onChange?.parse({
                    email: name === 'email' ? e.target.value : '<EMAIL>',
                    password: name === 'password' ? e.target.value : 'password123',
                  });
                } catch (error) {
                  mockValidate(error);
                }
              }}
            />
          ),
        };
        return children(field);
      },
    }));

    renderWithProvider(<LoginForm />);

    // Test email validation
    const emailInput = screen.getByLabelText('Email address');
    await userEvent.type(emailInput, 'invalid-email');
    expect(mockValidate).toHaveBeenCalledWith(
      expect.objectContaining({
        message: expect.stringContaining('valid email'),
      })
    );

    // Test password validation
    const passwordInput = screen.getByLabelText('Password');
    await userEvent.type(passwordInput, '12345');
    expect(mockValidate).toHaveBeenCalledWith(
      expect.objectContaining({
        message: expect.stringContaining('6 characters'),
      })
    );
  });
});

// Type Tests
describe('LoginForm types', () => {
  it('should have correct prop types', () => {
    expectTypeOf<LoginFormProps>().toMatchTypeOf<{
      onSuccess?: () => void;
    }>();
  });

  it('should have correct form value types', () => {
    type ExpectedFormValues = {
      email: string;
      password: string;
    };

    const loginSchema = z.object({
      email: z.email(),
      password: z.string().min(6),
    });

    type SchemaType = z.infer<typeof loginSchema>;
    expectTypeOf<SchemaType>().toEqualTypeOf<ExpectedFormValues>();
  });
});
