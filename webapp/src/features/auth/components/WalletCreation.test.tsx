import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { WalletCreation } from './WalletCreation';
import authReducer from '@/store/slices/authSlice';

// Mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: null,
        tokens: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        currentStep: 'wallet-creation',
        signUpEmail: null,
        ...initialState,
      },
    },
  });
};

const renderWalletCreation = (props = {}, storeState = {}) => {
  const store = createMockStore(storeState);
  return render(
    <Provider store={store}>
      <WalletCreation {...props} />
    </Provider>
  );
};

describe('WalletCreation', () => {
  it('should render wallet creation form', () => {
    renderWalletCreation();

    expect(screen.getByText("Let's create you a wallet")).toBeInTheDocument();
    expect(screen.getByText('Choose your coin')).toBeInTheDocument();
    expect(screen.getByText('Choose your chain')).toBeInTheDocument();
    expect(screen.getByText('Tether')).toBeInTheDocument();
    expect(screen.getByText('USDT')).toBeInTheDocument();
  });

  it('should render chain options', () => {
    renderWalletCreation();

    expect(screen.getByText('ERC-20')).toBeInTheDocument();
    expect(screen.getByText('Ethereum')).toBeInTheDocument();
    expect(screen.getByText('SOL')).toBeInTheDocument();
    expect(screen.getByText('Solana')).toBeInTheDocument();
    expect(screen.getByText('ARB')).toBeInTheDocument();
    expect(screen.getByText('Arbitrium')).toBeInTheDocument();
  });

  it('should have terms agreement checkbox', () => {
    renderWalletCreation();

    expect(screen.getByText('I agree to the Terms and Conditions')).toBeInTheDocument();
  });

  it('should disable continue button when terms not agreed', () => {
    renderWalletCreation();

    const continueButton = screen.getByRole('button', { name: /continue/i });
    expect(continueButton).toBeDisabled();
  });

  it('should enable continue button when terms agreed', () => {
    renderWalletCreation();

    const termsCheckbox = screen.getByText('I agree to the Terms and Conditions');
    fireEvent.click(termsCheckbox);

    const continueButton = screen.getByRole('button', { name: /continue/i });
    expect(continueButton).not.toBeDisabled();
  });

  it('should show loading state', () => {
    renderWalletCreation({}, { isLoading: true });

    const continueButton = screen.getByRole('button', { name: /creating/i });
    expect(continueButton).toBeDisabled();
  });

  it('should call onSuccess when wallet creation succeeds', async () => {
    const onSuccess = vi.fn();
    renderWalletCreation({ onSuccess });

    // Agree to terms
    const termsCheckbox = screen.getByText('I agree to the Terms and Conditions');
    fireEvent.click(termsCheckbox);

    // Click continue
    const continueButton = screen.getByRole('button', { name: /continue/i });
    fireEvent.click(continueButton);

    // Wait for async operation
    await new Promise(resolve => setTimeout(resolve, 100));
  });

  it('should render back button when onBack provided', () => {
    const onBack = vi.fn();
    renderWalletCreation({ onBack });

    expect(screen.getByText('Back')).toBeInTheDocument();
  });

  it('should call onBack when back button clicked', () => {
    const onBack = vi.fn();
    renderWalletCreation({ onBack });

    const backButton = screen.getByText('Back');
    fireEvent.click(backButton);

    expect(onBack).toHaveBeenCalled();
  });
});
