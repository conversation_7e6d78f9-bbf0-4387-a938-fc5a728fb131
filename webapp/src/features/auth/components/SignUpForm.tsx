import { useAppForm } from '@/components/forms/Form';
import { Button } from '@/components/ui/button';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { signUp, selectAuthLoading, selectAuthError, clearError } from '@/store/slices/authSlice';
import { useEffect } from 'react';
import z from 'zod/v4';

const signUpSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
});

export interface SignUpFormProps {
  onSuccess?: () => void;
}

export function SignUpForm({ onSuccess }: SignUpFormProps) {
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);

  const form = useAppForm({
    defaultValues: {
      email: '',
      password: '',
    },
    validators: {
      onChange: signUpSchema,
    },
    onSubmit: async ({ value }) => {
      const result = await dispatch(signUp(value));
      if (signUp.fulfilled.match(result)) {
        onSuccess?.();
      }
    },
  });

  // Clear error when component unmounts or form values change
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  useEffect(() => {
    if (error) {
      // Clear error after a delay
      const timer = setTimeout(() => {
        dispatch(clearError());
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, dispatch]);

  return (
    <form
      data-testid="signup-form"
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
      className="space-y-4"
    >
      <div className="space-y-6">
        <form.AppField
          name="email"
          children={(field) => (
            <field.TextField
              label="Email address"
              placeholder="<EMAIL>"
              type="email"
            />
          )}
        />

        <form.AppField
          name="password"
          children={(field) => (
            <field.TextField
              label="Password"
              placeholder="Enter your password"
              type="password"
            />
          )}
        />
      </div>

      {error && (
        <div className="text-red-400 text-sm bg-red-900/20 border border-red-800 rounded-lg p-3 text-center">
          {error}
        </div>
      )}

      <Button
        type="submit"
        className="w-full h-12 bg-[#C4FF61] text-black hover:bg-[#B8F055] disabled:opacity-50 disabled:cursor-not-allowed font-medium rounded-lg transition-all duration-200"
        disabled={isLoading || !form.state.canSubmit}
      >
        {isLoading ? 'Creating Account...' : 'Continue'}
      </Button>
    </form>
  );
}
