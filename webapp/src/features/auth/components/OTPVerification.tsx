import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { 
  verifyOTP, 
  resendOTP, 
  selectAuthLoading, 
  selectAuthError, 
  selectSignUpEmail,
  clearError 
} from '@/store/slices/authSlice';
import { OTPInput } from './OTPInput';
import { ArrowLeft } from 'lucide-react';

interface OTPVerificationProps {
  onSuccess?: () => void;
  onBack?: () => void;
}

export function OTPVerification({ onSuccess, onBack }: OTPVerificationProps) {
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);
  const email = useAppSelector(selectSignUpEmail);



  const [otpCode, setOtpCode] = useState('');
  const [countdown, setCountdown] = useState(120); // 2 minutes
  const [canResend, setCanResend] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false); // Prevent multiple submissions

  // Countdown timer
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [countdown]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const handleVerifyOTP = async () => {
    if (!email || otpCode.length !== 6 || isVerifying || isLoading) return;

    setIsVerifying(true);

    try {
      const result = await dispatch(verifyOTP({
        email,
        code: { code: otpCode }
      }));

      if (verifyOTP.fulfilled.match(result)) {
        onSuccess?.();
      }
    } finally {
      setIsVerifying(false);
    }
  };

  // Remove auto-submit to prevent infinite loops
  // Users will need to click the Continue button

  const handleResendOTP = async () => {
    if (!email || !canResend) return;
    
    const result = await dispatch(resendOTP(email));
    
    if (resendOTP.fulfilled.match(result)) {
      setCountdown(120);
      setCanResend(false);
      setOtpCode('');
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!email) {
    return (
      <div className="text-center text-red-400">
        Email not found. Please start the sign-up process again.
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        {onBack && (
          <button
            onClick={onBack}
            className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors mb-6"
            disabled={isLoading}
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </button>
        )}

        <h2 className="text-3xl font-semibold text-white">
          Enter the OTP code
        </h2>

        <p className="text-gray-400 text-sm leading-relaxed">
          We have sent you a confirmation code to{' '}
          <span className="text-white font-medium">{email}</span>
          <br />
          Insert code to verify your email.
        </p>
      </div>

      {/* OTP Input */}
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-4">
            Confirmation code
          </label>
          <OTPInput
            value={otpCode}
            onChange={setOtpCode}
            disabled={isLoading}
            length={6}
          />
        </div>

        {error && (
          <div className="text-red-400 text-sm bg-red-900/20 border border-red-800 rounded-lg p-3 text-center">
            {error}
          </div>
        )}
      </div>

      {/* Support and Resend */}
      <div className="text-center space-y-3">
        <p className="text-gray-400 text-sm">
          Can't receive the code?{' '}
          <button
            onClick={() => {/* TODO: Add support contact */}}
            className="text-[#C4FF61] hover:underline"
          >
            Contact support
          </button>
        </p>

        <div className="text-sm">
          {canResend ? (
            <button
              onClick={handleResendOTP}
              disabled={isLoading}
              className="text-[#C4FF61] hover:underline disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Resend code
            </button>
          ) : (
            <span className="text-gray-400">
              Resend code in {formatTime(countdown)}
            </span>
          )}
        </div>
      </div>

      {/* Continue Button */}
      <Button
        onClick={handleVerifyOTP}
        disabled={otpCode.length !== 6 || isLoading}
        className="w-full h-12 bg-[#C4FF61] text-black hover:bg-[#B8F055] disabled:opacity-50 disabled:cursor-not-allowed font-medium rounded-lg transition-all duration-200"
      >
        {isLoading ? 'Verifying...' : 'Continue'}
      </Button>
    </div>
  );
}
