import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';

interface EmailConfirmedProps {
  onContinue?: () => void;
}

export function EmailConfirmed({ onContinue }: EmailConfirmedProps) {
  return (
    <div className="w-full max-w-md mx-auto">
      {/* Modal-like container */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-8 text-center space-y-6">
        {/* Success Icon */}
        <div className="flex justify-center">
          <div className="w-16 h-16 bg-[#C4FF61] rounded-full flex items-center justify-center">
            <Check className="w-8 h-8 text-black" strokeWidth={3} />
          </div>
        </div>

        {/* Success Message */}
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-[#C4FF61]">
            EMAIL CONFIRMED
          </h2>
          <p className="text-gray-400">
            Your email address has been successfully verified.
          </p>
        </div>

        {/* Continue Button */}
        <Button
          onClick={onContinue}
          className="w-full bg-[#C4FF61] text-black hover:bg-[#B8F055] font-medium"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
