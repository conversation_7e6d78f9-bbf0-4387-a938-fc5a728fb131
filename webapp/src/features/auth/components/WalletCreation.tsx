import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Check } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { createWallet, selectAuthLoading } from '@/store/slices/authSlice';

interface WalletCreationProps {
  onSuccess?: () => void;
  onBack?: () => void;
}

export function WalletCreation({ onSuccess, onBack }: WalletCreationProps) {
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(selectAuthLoading);

  const [selectedCoin, setSelectedCoin] = useState<string>('tether');
  const [selectedChain, setSelectedChain] = useState<string>('ethereum');
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const coins = [
    { id: 'tether', name: 'Tether', symbol: 'USDT' }
  ];

  const chains = [
    { id: 'ethereum', name: 'ERC-20', network: 'Ethereum' },
    { id: 'solana', name: 'SOL', network: 'Solana' },
    { id: 'arbitrum', name: 'ARB', network: 'Arbitrium' }
  ];

  const handleCreateWallet = async () => {
    if (!agreedToTerms) return;

    try {
      await dispatch(createWallet({
        coin: selectedCoin,
        chain: selectedChain
      })).unwrap();
      onSuccess?.();
    } catch (error) {
      console.error('Wallet creation failed:', error);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        {onBack && (
          <button
            onClick={onBack}
            className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors mb-6"
            disabled={isLoading}
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </button>
        )}
        
        <h2 className="text-3xl font-semibold text-white">
          Let's create you a wallet
        </h2>
      </div>

      {/* Coin Selection */}
      <div className="space-y-4">
        <h3 className="text-white font-medium">Choose your coin</h3>
        <div className="space-y-2">
          {coins.map((coin) => (
            <div
              key={coin.id}
              onClick={() => setSelectedCoin(coin.id)}
              className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
                selectedCoin === coin.id
                  ? 'border-[#C4FF61] bg-[#C4FF61]/10'
                  : 'border-gray-600 bg-[#2a2a2a] hover:border-gray-500'
              }`}
            >
              <div className="flex items-center gap-3">
                <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                  selectedCoin === coin.id
                    ? 'border-[#C4FF61] bg-[#C4FF61]'
                    : 'border-gray-500'
                }`}>
                  {selectedCoin === coin.id && (
                    <div className="w-2 h-2 bg-black rounded-full" />
                  )}
                </div>
                <div>
                  <div className="text-white font-medium">{coin.name}</div>
                  <div className="text-gray-400 text-sm">{coin.symbol}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Chain Selection */}
      <div className="space-y-4">
        <h3 className="text-white font-medium">Choose your chain</h3>
        <div className="grid grid-cols-1 gap-2">
          {chains.map((chain) => (
            <div
              key={chain.id}
              onClick={() => setSelectedChain(chain.id)}
              className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
                selectedChain === chain.id
                  ? 'border-[#C4FF61] bg-[#C4FF61]/10'
                  : 'border-gray-600 bg-[#2a2a2a] hover:border-gray-500'
              }`}
            >
              <div className="flex items-center gap-3">
                <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                  selectedChain === chain.id
                    ? 'border-[#C4FF61] bg-[#C4FF61]'
                    : 'border-gray-500'
                }`}>
                  {selectedChain === chain.id && (
                    <div className="w-2 h-2 bg-black rounded-full" />
                  )}
                </div>
                <div>
                  <div className="text-white font-medium">{chain.name}</div>
                  <div className="text-gray-400 text-sm">{chain.network}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Terms Agreement */}
      <div className="space-y-4">
        <div className="flex items-start gap-3">
          <button
            onClick={() => setAgreedToTerms(!agreedToTerms)}
            className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${
              agreedToTerms
                ? 'border-[#C4FF61] bg-[#C4FF61]'
                : 'border-gray-500 hover:border-gray-400'
            }`}
          >
            {agreedToTerms && <Check className="w-3 h-3 text-black" />}
          </button>
          <label className="text-gray-300 text-sm cursor-pointer" onClick={() => setAgreedToTerms(!agreedToTerms)}>
            I agree to the Terms and Conditions
          </label>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        {onBack && (
          <Button
            onClick={onBack}
            variant="outline"
            className="flex-1 h-12 border-gray-600 text-gray-300 hover:bg-gray-800"
            disabled={isLoading}
          >
            Back
          </Button>
        )}
        
        <Button
          onClick={handleCreateWallet}
          disabled={!agreedToTerms || isLoading}
          className="flex-1 h-12 bg-[#C4FF61] text-black hover:bg-[#B8F055] disabled:opacity-50 disabled:cursor-not-allowed font-medium rounded-lg transition-all duration-200"
        >
          {isLoading ? 'Creating...' : 'Continue'}
        </Button>
      </div>
    </div>
  );
}
