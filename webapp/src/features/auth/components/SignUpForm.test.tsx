import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import authReducer from '@/store/slices/authSlice';
import { SignUpForm } from './SignUpForm';

// Mock the form hook
vi.mock('@/components/forms/Form', () => ({
  useAppForm: vi.fn(() => ({
    state: { canSubmit: true },
    handleSubmit: vi.fn(),
    AppField: ({ children, name }: any) => {
      const field = {
        TextField: ({ label, type, placeholder, className, disabled }: any) => (
          <div>
            <label>{label}</label>
            <input
              type={type}
              placeholder={placeholder}
              className={className}
              disabled={disabled}
              data-testid={`input-${name}`}
            />
          </div>
        ),
      };
      return children(field);
    },
  })),
}));

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: null,
        tokens: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        currentStep: 'sign-up',
        signUpEmail: null,
        ...initialState,
      },
    },
  });
};

describe('SignUpForm', () => {
  let store: ReturnType<typeof createMockStore>;
  const mockOnSuccess = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    store = createMockStore();
  });

  const renderSignUpForm = (props = {}) => {
    return render(
      <Provider store={store}>
        <SignUpForm onSuccess={mockOnSuccess} {...props} />
      </Provider>
    );
  };

  it('renders form fields correctly', () => {
    renderSignUpForm();

    expect(screen.getByTestId('signup-form')).toBeInTheDocument();
    expect(screen.getByLabelText('Email address')).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /continue/i })).toBeInTheDocument();
  });

  it('shows loading state when submitting', () => {
    store = createMockStore({ isLoading: true });
    renderSignUpForm();

    const submitButton = screen.getByRole('button');
    expect(submitButton).toHaveTextContent('Creating Account...');
    expect(submitButton).toBeDisabled();
  });

  it('displays error message when there is an error', () => {
    const errorMessage = 'Email already exists';
    store = createMockStore({ error: errorMessage });
    renderSignUpForm();

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('disables form fields when loading', () => {
    store = createMockStore({ isLoading: true });
    renderSignUpForm();

    expect(screen.getByTestId('input-email')).toBeDisabled();
    expect(screen.getByTestId('input-password')).toBeDisabled();
  });

  it('has correct styling classes', () => {
    renderSignUpForm();

    const form = screen.getByTestId('signup-form');
    expect(form).toHaveClass('space-y-4');

    const button = screen.getByRole('button', { name: /continue/i });
    expect(button).toHaveClass('bg-primary', 'text-black');
  });
});
