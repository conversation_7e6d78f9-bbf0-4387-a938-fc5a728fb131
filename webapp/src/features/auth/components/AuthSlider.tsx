import { useState, useEffect } from 'react';
import invoicingImage from '@/assets/images/523416042ce370de632c87cff79e68579797f152.png';

interface SlideData {
  id: number;
  title: string;
  image: string;
}

const slides: SlideData[] = [
  {
    id: 1,
    title: 'Invoicing',
    image: invoicingImage,
  },
  {
    id: 2,
    title: 'Invoicing',
    image: invoicingImage,
  },
  {
    id: 3,
    title: 'Invoicing',
    image: invoicingImage,
  },
];

export function AuthSlider() {
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 4000); // Change slide every 4 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative w-full h-full bg-black overflow-hidden">
      {/* Slides */}
      <div className="relative w-full h-full">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            {/* Title */}
            <div className="absolute top-8 left-8 z-10">
              <h2 className="text-white text-2xl font-medium">
                {slide.title}
              </h2>
            </div>

            {/* Image */}
            <div className="absolute inset-0 flex items-center justify-center">
              <img
                src={slide.image}
                alt={slide.title}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          </div>
        ))}
      </div>

      {/* Dots indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            type="button"
            onClick={() => setCurrentSlide(index)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? 'bg-[#C4FF61] w-6'
                : 'bg-gray-600 hover:bg-gray-500'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
