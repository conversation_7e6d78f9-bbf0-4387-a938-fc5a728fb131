import { useAppForm } from '@/components/forms/Form';
import { Button } from '@/components/ui/button';
import { store } from '@/store';
import { setUser } from '@/store/slices/authSlice';
import { useNavigate } from '@tanstack/react-router';
import z from 'zod/v4';

const loginSchema = z.object({
  email: z.email('Please enter a valid email'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export interface LoginFormProps {
  onSuccess?: () => void;
}

export function LoginForm({ onSuccess }: LoginFormProps) {
  const navigate = useNavigate();

  const form = useAppForm({
    defaultValues: {
      email: '',
      password: '',
    },
    validators: {
      onChange: loginSchema,
    },
    onSubmit: async ({ value }) => {
      store.dispatch(
        setUser({
          id: '1',
          email: value.email,
        })
      );
      navigate({ to: '/dashboard' });
      onSuccess?.();
    },
  });

  return (
    <form
      data-testid="login-form"
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="grid gap-4">
        <div className="grid gap-2">
          <form.AppField
            name="email"
            children={(field) => <field.TextField label="Email" type="email" />}
          />
        </div>
        <div className="grid gap-2">
          <form.AppField
            name="password"
            children={(field) => <field.TextField label="Password" type="password" />}
          />
        </div>
      </div>
      <div className="mt-4">
        <Button type="submit" className="w-full">
          Login
        </Button>
      </div>
    </form>
  );
}
