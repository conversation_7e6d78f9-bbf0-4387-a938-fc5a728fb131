import { cn } from '@/lib/utils';
import { useFieldContext } from '@/components/forms/FormContext';
import { useState } from 'react';

export interface AuthTextFieldProps {
  label: string;
  placeholder: string;
  type?: 'text' | 'email' | 'password';
  className?: string;
  disabled?: boolean;
}

export function AuthTextField({
  label,
  placeholder,
  type = 'text',
  className,
  disabled = false,
}: AuthTextFieldProps) {
  const field = useFieldContext<string>();
  const [isFocused, setIsFocused] = useState(false);
  const hasValue = field.state.value && field.state.value.length > 0;
  const hasError = field.state.meta.errors && field.state.meta.errors.length > 0;

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label */}
      <label 
        htmlFor={field.name}
        className="block text-sm font-medium text-gray-300"
      >
        {label}
      </label>

      {/* Input Container */}
      <div className="relative">
        <input
          id={field.name}
          name={field.name}
          type={type}
          value={field.state.value || ''}
          placeholder={placeholder}
          disabled={disabled}
          onFocus={() => setIsFocused(true)}
          onBlur={() => {
            setIsFocused(false);
            field.handleBlur();
          }}
          onChange={(e) => field.handleChange(e.target.value)}
          className={cn(
            // Base styles
            'w-full h-12 px-4 py-3 rounded-lg transition-all duration-200',
            'text-white placeholder-gray-500 text-sm',
            'border border-gray-600 bg-[#2a2a2a]',
            
            // Focus styles
            'focus:outline-none focus:ring-2 focus:ring-[#C4FF61] focus:border-transparent',
            
            // Hover styles
            'hover:border-gray-500',
            
            // Error styles
            hasError && 'border-red-500 focus:ring-red-500',
            
            // Disabled styles
            disabled && 'opacity-50 cursor-not-allowed bg-gray-800',
            
            // Active/filled styles
            (isFocused || hasValue) && !hasError && 'border-[#C4FF61]'
          )}
        />
      </div>

      {/* Error Message */}
      {hasError && (
        <div className="text-red-400 text-xs mt-1">
          {field.state.meta.errors[0]}
        </div>
      )}
    </div>
  );
}
