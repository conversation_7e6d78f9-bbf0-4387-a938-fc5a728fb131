
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { selectCurrentStep, setCurrentStep } from '@/store/slices/authSlice';
import { LoginForm } from './LoginForm';
import { SignUpForm } from './SignUpForm';
import { OTPVerification } from './OTPVerification';
import { EmailConfirmed } from './EmailConfirmed';
import { BusinessDetailsForm } from './BusinessDetailsForm';

interface AuthContainerProps {
  onSuccess?: () => void;
}

export function AuthContainer({ onSuccess }: AuthContainerProps) {
  const dispatch = useAppDispatch();
  const currentStep = useAppSelector(selectCurrentStep);

  // Sync activeTab with currentStep from Redux
  const activeTab = (currentStep === 'sign-up') ? 'sign-up' : 'sign-in';

  const handleTabChange = (tab: 'sign-in' | 'sign-up') => {
    dispatch(setCurrentStep(tab));
  };

  const handleSignUpSuccess = () => {
    // Step will be automatically updated by Redux
  };

  const handleOTPSuccess = () => {
    // Step will be automatically updated by Redux
  };

  const handleEmailConfirmedContinue = () => {
    dispatch(setCurrentStep('business-details'));
  };

  const handleBusinessDetailsSuccess = () => {
    dispatch(setCurrentStep('completed'));
    onSuccess?.();
  };

  const handleBackToSignUp = () => {
    dispatch(setCurrentStep('sign-up'));
  };

  // Render different steps based on current step
  const renderStep = () => {
    switch (currentStep) {
      case 'sign-in':
        return <LoginForm onSuccess={onSuccess} />;

      case 'sign-up':
        return <SignUpForm onSuccess={handleSignUpSuccess} />;

      case 'otp-verification':
        return (
          <OTPVerification
            onSuccess={handleOTPSuccess}
            onBack={handleBackToSignUp}
          />
        );

      case 'email-confirmed':
        return <EmailConfirmed onContinue={handleEmailConfirmedContinue} />;

      case 'business-details':
        return <BusinessDetailsForm onSuccess={handleBusinessDetailsSuccess} />;

      default:
        return <LoginForm onSuccess={onSuccess} />;
    }
  };

  // Show tabs only for sign-in and sign-up steps
  const showTabs = currentStep === 'sign-in' || currentStep === 'sign-up';

  return (
    <div className="w-full max-w-md mx-auto">
      {/* WeFi Logo */}
      <div className="text-center mb-8">
        <div className="text-[#C4FF61] text-3xl font-bold tracking-wide flex items-center justify-center gap-2">
          <span className="text-4xl">🅦</span>
          <span>WeFi</span>
        </div>
      </div>

      {/* Auth Card */}
      <div className="bg-[#1a1a1a] border border-gray-800 rounded-2xl p-8 w-full max-w-[400px]">
        {showTabs && (
          <>
            {/* Tab Navigation */}
            <div className="flex mb-8 bg-[#2a2a2a] rounded-full p-1">
              <button
                onClick={() => handleTabChange('sign-in')}
                className={`flex-1 py-3 px-6 rounded-full text-sm font-medium transition-all duration-200 ${
                  activeTab === 'sign-in'
                    ? 'bg-[#C4FF61] text-black shadow-lg'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                Sign In
              </button>
              <button
                onClick={() => handleTabChange('sign-up')}
                className={`flex-1 py-3 px-6 rounded-full text-sm font-medium transition-all duration-200 ${
                  activeTab === 'sign-up'
                    ? 'bg-[#C4FF61] text-black shadow-lg'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                Sign Up
              </button>
            </div>

            {/* Welcome Text */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-semibold text-white mb-2">
                Welcome
              </h1>
              <p className="text-gray-400 text-sm">
                {activeTab === 'sign-in'
                  ? 'Sign in using your email'
                  : 'Create your account'
                }
              </p>
            </div>
          </>
        )}

        {/* Render Current Step */}
        {renderStep()}
      </div>
    </div>
  );
}
