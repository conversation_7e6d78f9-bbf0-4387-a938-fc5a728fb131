
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { selectCurrentStep, setCurrentStep } from '@/store/slices/authSlice';
import { LoginForm } from './LoginForm';
import { SignUpForm } from './SignUpForm';
import { OTPVerification } from './OTPVerification';
import { EmailConfirmed } from './EmailConfirmed';
import { WalletCreation } from './WalletCreation';
import { WalletCreated } from './WalletCreated';
import { AuthSlider } from './AuthSlider';

interface AuthContainerProps {
  onSuccess?: () => void;
}

export function AuthContainer({ onSuccess }: AuthContainerProps) {
  const dispatch = useAppDispatch();
  const currentStep = useAppSelector(selectCurrentStep);

  // Sync activeTab with currentStep from Redux
  const activeTab = (currentStep === 'sign-up') ? 'sign-up' : 'sign-in';

  const handleTabChange = (tab: 'sign-in' | 'sign-up') => {
    dispatch(setCurrentStep(tab));
  };

  const handleSignUpSuccess = () => {
    // Step will be automatically updated by Redux
  };

  const handleOTPSuccess = () => {
    // Step will be automatically updated by Redux
  };

  const handleEmailConfirmedContinue = () => {
    dispatch(setCurrentStep('wallet-creation'));
  };

  const handleWalletCreationSuccess = () => {
    // The step will be automatically updated by Redux based on the createWallet result
    // If user is authenticated, it will go to 'completed', otherwise to 'wallet-created'
  };

  const handleWalletCreatedContinue = () => {
    dispatch(setCurrentStep('completed'));
    onSuccess?.();
  };

  const handleBackToEmailConfirmed = () => {
    dispatch(setCurrentStep('email-confirmed'));
  };

  const handleBackToSignUp = () => {
    dispatch(setCurrentStep('sign-up'));
  };

  // Render different steps based on current step
  const renderStep = () => {
    switch (currentStep) {
      case 'sign-in':
        return <LoginForm onSuccess={onSuccess} />;

      case 'sign-up':
        return <SignUpForm onSuccess={handleSignUpSuccess} />;

      case 'otp-verification':
        return (
          <OTPVerification
            onSuccess={handleOTPSuccess}
            onBack={handleBackToSignUp}
          />
        );

      case 'email-confirmed':
        return <EmailConfirmed onContinue={handleEmailConfirmedContinue} />;

      case 'wallet-creation':
        return (
          <WalletCreation
            onSuccess={handleWalletCreationSuccess}
            onBack={handleBackToEmailConfirmed}
          />
        );

      case 'wallet-created':
        return <WalletCreated onContinue={handleWalletCreatedContinue} />;

      case 'completed':
        onSuccess?.();
        return null;

      default:
        return <LoginForm onSuccess={onSuccess} />;
    }
  };

  // Show tabs only for sign-in and sign-up steps
  const showTabs = currentStep === 'sign-in' || currentStep === 'sign-up';

  // Show card for most steps, but not for wallet creation steps
  const showCard = !['wallet-creation', 'wallet-created'].includes(currentStep);

  return (
    <div className="min-h-screen bg-black flex">
      {/* Left side - Slider */}
      <div className="flex-1 relative">
        <AuthSlider />
      </div>

      {/* Right side - Form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* WeFi Logo */}
          <div className="mb-8">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-[#C4FF61] rounded flex items-center justify-center">
                <span className="text-black font-bold text-sm">W</span>
              </div>
              <span className="text-white text-xl font-medium">WeFi</span>
            </div>
          </div>

          {/* Auth Content */}
          {showCard ? (
            <div className="space-y-6">
              {showTabs && (
                <>
                  {/* Tab Navigation */}
                  <div className="flex bg-[#2a2a2a] rounded-lg p-1">
                    <button
                      type="button"
                      onClick={() => handleTabChange('sign-in')}
                      className={`flex-1 py-3 px-6 rounded-md text-sm font-medium transition-all duration-200 ${
                        activeTab === 'sign-in'
                          ? 'bg-[#C4FF61] text-black'
                          : 'text-gray-400 hover:text-white'
                      }`}
                    >
                      Sign In
                    </button>
                    <button
                      type="button"
                      onClick={() => handleTabChange('sign-up')}
                      className={`flex-1 py-3 px-6 rounded-md text-sm font-medium transition-all duration-200 ${
                        activeTab === 'sign-up'
                          ? 'bg-[#C4FF61] text-black'
                          : 'text-gray-400 hover:text-white'
                      }`}
                    >
                      Sign Up
                    </button>
                  </div>

                  {/* Title and Description */}
                  <div className="space-y-2">
                    <h1 className="text-2xl font-semibold text-white">
                      {activeTab === 'sign-in' ? 'Sign In' : 'Sign Up'}
                    </h1>
                    <p className="text-gray-400 text-sm">
                      {activeTab === 'sign-in'
                        ? 'Sign in using your email'
                        : 'Create a new account'
                      }
                    </p>
                  </div>
                </>
              )}

              {/* Render Current Step */}
              {renderStep()}
            </div>
          ) : (
            /* Direct rendering for wallet steps */
            renderStep()
          )}
        </div>
      </div>
    </div>
  );
}
