import { useState } from 'react';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { selectCurrentStep, setCurrentStep } from '@/store/slices/authSlice';
import { LoginForm } from './LoginForm';
import { SignUpForm } from './SignUpForm';
import { OTPVerification } from './OTPVerification';
import { EmailConfirmed } from './EmailConfirmed';
import { BusinessDetailsForm } from './BusinessDetailsForm';

interface AuthContainerProps {
  onSuccess?: () => void;
}

export function AuthContainer({ onSuccess }: AuthContainerProps) {
  const dispatch = useAppDispatch();
  const currentStep = useAppSelector(selectCurrentStep);
  const [activeTab, setActiveTab] = useState<'sign-in' | 'sign-up'>('sign-in');

  const handleTabChange = (tab: 'sign-in' | 'sign-up') => {
    setActiveTab(tab);
    dispatch(setCurrentStep(tab));
  };

  const handleSignUpSuccess = () => {
    // Step will be automatically updated by Redux
  };

  const handleOTPSuccess = () => {
    // Step will be automatically updated by Redux
  };

  const handleEmailConfirmedContinue = () => {
    dispatch(setCurrentStep('business-details'));
  };

  const handleBusinessDetailsSuccess = () => {
    dispatch(setCurrentStep('completed'));
    onSuccess?.();
  };

  const handleBackToSignUp = () => {
    dispatch(setCurrentStep('sign-up'));
  };

  // Render different steps based on current step
  const renderStep = () => {
    switch (currentStep) {
      case 'sign-in':
        return <LoginForm onSuccess={onSuccess} />;
      
      case 'sign-up':
        return <SignUpForm onSuccess={handleSignUpSuccess} />;
      
      case 'otp-verification':
        return (
          <OTPVerification 
            onSuccess={handleOTPSuccess}
            onBack={handleBackToSignUp}
          />
        );
      
      case 'email-confirmed':
        return <EmailConfirmed onContinue={handleEmailConfirmedContinue} />;
      
      case 'business-details':
        return <BusinessDetailsForm onSuccess={handleBusinessDetailsSuccess} />;
      
      default:
        return <LoginForm onSuccess={onSuccess} />;
    }
  };

  // Show tabs only for sign-in and sign-up steps
  const showTabs = currentStep === 'sign-in' || currentStep === 'sign-up';

  return (
    <div className="w-full max-w-md mx-auto">
      {/* WeFi Logo */}
      <div className="text-center mb-8">
        <div className="text-[#C4FF61] text-2xl font-bold">
          WeFi
        </div>
      </div>

      {/* Auth Card */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-8">
        {showTabs && (
          <>
            {/* Tab Navigation */}
            <div className="flex mb-6 bg-gray-800 rounded-lg p-1">
              <button
                onClick={() => handleTabChange('sign-in')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'sign-in'
                    ? 'bg-[#C4FF61] text-black'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                Sign In
              </button>
              <button
                onClick={() => handleTabChange('sign-up')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'sign-up'
                    ? 'bg-[#C4FF61] text-black'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                Sign Up
              </button>
            </div>

            {/* Welcome Text */}
            <div className="text-center mb-6">
              <h1 className="text-2xl font-semibold text-white mb-2">
                Welcome
              </h1>
              <p className="text-gray-400">
                {activeTab === 'sign-in' 
                  ? 'Sign in using your email' 
                  : 'Create your account'
                }
              </p>
            </div>
          </>
        )}

        {/* Render Current Step */}
        {renderStep()}
      </div>
    </div>
  );
}
