import { useAppForm } from '@/components/forms/Form';
import { Button } from '@/components/ui/button';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { 
  submitBusinessDetails, 
  selectAuthLoading, 
  selectAuthError, 
  selectSignUpEmail,
  clearError 
} from '@/store/slices/authSlice';
import { useEffect } from 'react';
import z from 'zod/v4';

const businessDetailsSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  addressLine1: z.string().min(1, 'Address line 1 is required'),
  addressLine2: z.string().optional(),
  state: z.string().min(1, 'State/Region is required'),
  city: z.string().min(1, 'City is required'),
  zipCode: z.string().min(1, 'Zip code is required'),
});

export interface BusinessDetailsFormProps {
  onSuccess?: () => void;
}

export function BusinessDetailsForm({ onSuccess }: BusinessDetailsFormProps) {
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);
  const email = useAppSelector(selectSignUpEmail);

  const form = useAppForm({
    defaultValues: {
      firstName: '',
      lastName: '',
      addressLine1: '',
      addressLine2: '',
      state: '',
      city: '',
      zipCode: '',
    },
    validators: {
      onChange: businessDetailsSchema,
    },
    onSubmit: async ({ value }) => {
      if (!email) return;
      
      const result = await dispatch(submitBusinessDetails({ 
        email, 
        data: value 
      }));
      
      if (submitBusinessDetails.fulfilled.match(result)) {
        onSuccess?.();
      }
    },
  });

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  if (!email) {
    return (
      <div className="text-center text-red-400">
        Email not found. Please start the sign-up process again.
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold text-white">
          Fill Business Details
        </h2>
        <p className="text-gray-400">
          Please provide your personal and business information to complete your registration.
        </p>
      </div>

      {/* Form */}
      <form
        data-testid="business-details-form"
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
        className="space-y-4"
      >
        {/* Name Fields */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <form.AppField
              name="firstName"
              children={(field) => (
                <field.TextField
                  label="First Name"
                  placeholder="John"
                  className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                  disabled={isLoading}
                />
              )}
            />
          </div>
          <div>
            <form.AppField
              name="lastName"
              children={(field) => (
                <field.TextField
                  label="Last Name"
                  placeholder="Doe"
                  className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                  disabled={isLoading}
                />
              )}
            />
          </div>
        </div>

        {/* Address Fields */}
        <div>
          <form.AppField
            name="addressLine1"
            children={(field) => (
              <field.TextField
                label="Address Line 1"
                placeholder="123 Main Street"
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                disabled={isLoading}
              />
            )}
          />
        </div>

        <div>
          <form.AppField
            name="addressLine2"
            children={(field) => (
              <field.TextField
                label="Address Line 2 (Optional)"
                placeholder="Apartment, suite, etc."
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                disabled={isLoading}
              />
            )}
          />
        </div>

        {/* Location Fields */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <form.AppField
              name="state"
              children={(field) => (
                <field.TextField
                  label="State/Region"
                  placeholder="California"
                  className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                  disabled={isLoading}
                />
              )}
            />
          </div>
          <div>
            <form.AppField
              name="city"
              children={(field) => (
                <field.TextField
                  label="City"
                  placeholder="San Francisco"
                  className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                  disabled={isLoading}
                />
              )}
            />
          </div>
        </div>

        <div>
          <form.AppField
            name="zipCode"
            children={(field) => (
              <field.TextField
                label="Zip Code"
                placeholder="94102"
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                disabled={isLoading}
              />
            )}
          />
        </div>

        {error && (
          <div className="text-red-400 text-sm bg-red-900/20 border border-red-800 rounded-md p-3">
            {error}
          </div>
        )}

        <Button
          type="submit"
          className="w-full bg-[#C4FF61] text-black hover:bg-[#B8F055] disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={isLoading || !form.state.canSubmit}
        >
          {isLoading ? 'Creating Account...' : 'Continue'}
        </Button>
      </form>
    </div>
  );
}
