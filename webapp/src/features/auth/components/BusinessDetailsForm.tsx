import { useAppForm } from '@/components/forms/Form';
import { Button } from '@/components/ui/button';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  submitBusinessDetails,
  selectAuthLoading,
  selectAuthError,
  selectSignUpEmail,
  clearError
} from '@/store/slices/authSlice';
import { useEffect } from 'react';
import { AuthTextField } from './AuthTextField';
import z from 'zod/v4';

const businessDetailsSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  addressLine1: z.string().min(1, 'Address line 1 is required'),
  addressLine2: z.string().optional(),
  state: z.string().min(1, 'State/Region is required'),
  city: z.string().min(1, 'City is required'),
  zipCode: z.string().min(1, 'Zip code is required'),
});

export interface BusinessDetailsFormProps {
  onSuccess?: () => void;
}

export function BusinessDetailsForm({ onSuccess }: BusinessDetailsFormProps) {
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);
  const email = useAppSelector(selectSignUpEmail);

  const form = useAppForm({
    defaultValues: {
      firstName: '',
      lastName: '',
      addressLine1: '',
      addressLine2: '',
      state: '',
      city: '',
      zipCode: '',
    },
    validators: {
      onChange: businessDetailsSchema,
    },
    onSubmit: async ({ value }) => {
      if (!email) return;
      
      const result = await dispatch(submitBusinessDetails({ 
        email, 
        data: value 
      }));
      
      if (submitBusinessDetails.fulfilled.match(result)) {
        onSuccess?.();
      }
    },
  });

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  if (!email) {
    return (
      <div className="text-center text-red-400">
        Email not found. Please start the sign-up process again.
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold text-white">
          Fill Business Details
        </h2>
        <p className="text-gray-400">
          Please provide your personal and business information to complete your registration.
        </p>
      </div>

      {/* Form */}
      <form
        data-testid="business-details-form"
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
        className="space-y-4"
      >
        {/* Name Fields */}
        <div className="grid grid-cols-2 gap-4">
          <form.AppField
            name="firstName"
            children={(field) => (
              <AuthTextField
                label="First Name"
                placeholder="John"
                disabled={isLoading}
              />
            )}
          />
          <form.AppField
            name="lastName"
            children={(field) => (
              <AuthTextField
                label="Last Name"
                placeholder="Doe"
                disabled={isLoading}
              />
            )}
          />
        </div>

        {/* Address Fields */}
        <form.AppField
          name="addressLine1"
          children={(field) => (
            <AuthTextField
              label="Address Line 1"
              placeholder="123 Main Street"
              disabled={isLoading}
            />
          )}
        />

        <form.AppField
          name="addressLine2"
          children={(field) => (
            <AuthTextField
              label="Address Line 2 (Optional)"
              placeholder="Apartment, suite, etc."
              disabled={isLoading}
            />
          )}
        />

        {/* Location Fields */}
        <div className="grid grid-cols-2 gap-4">
          <form.AppField
            name="state"
            children={(field) => (
              <AuthTextField
                label="State/Region"
                placeholder="California"
                disabled={isLoading}
              />
            )}
          />
          <form.AppField
            name="city"
            children={(field) => (
              <AuthTextField
                label="City"
                placeholder="San Francisco"
                disabled={isLoading}
              />
            )}
          />
        </div>

        <form.AppField
          name="zipCode"
          children={(field) => (
            <AuthTextField
              label="Zip Code"
              placeholder="94102"
              disabled={isLoading}
            />
          )}
        />

        {error && (
          <div className="text-red-400 text-sm bg-red-900/20 border border-red-800 rounded-lg p-3 text-center">
            {error}
          </div>
        )}

        <Button
          type="submit"
          className="w-full h-12 bg-[#C4FF61] text-black hover:bg-[#B8F055] disabled:opacity-50 disabled:cursor-not-allowed font-medium rounded-lg transition-all duration-200"
          disabled={isLoading || !form.state.canSubmit}
        >
          {isLoading ? 'Creating Account...' : 'Continue'}
        </Button>
      </form>
    </div>
  );
}
