// Auth form types
export interface SignUpFormData {
  email: string;
  password: string;
}

export interface SignInFormData {
  email: string;
  password: string;
}

export interface OTPFormData {
  code: string;
}

export interface BusinessDetailsFormData {
  firstName: string;
  lastName: string;
  addressLine1: string;
  addressLine2?: string;
  state: string;
  city: string;
  zipCode: string;
}

// JWT Token types
export interface JWTTokens {
  accessToken: string;
  refreshToken: string;
}

export interface DecodedJWTPayload {
  sub: string; // user id
  email: string;
  exp: number;
  iat: number;
}

// User types
export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  isEmailVerified: boolean;
  businessDetails?: BusinessDetailsFormData;
}

// API Response types
export interface AuthApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface SignUpResponse {
  message: string;
  email: string;
}

export interface SignInResponse {
  user: User;
  tokens: JWTTokens;
}

export interface OTPVerificationResponse {
  message: string;
  isVerified: boolean;
}

export interface BusinessDetailsResponse {
  user: User;
  message: string;
}

// Auth flow step types
export type AuthStep =
  | 'sign-in'
  | 'sign-up'
  | 'otp-verification'
  | 'email-confirmed'
  | 'wallet-creation'
  | 'wallet-created'
  | 'completed';

// Auth state types
export interface AuthState {
  user: User | null;
  tokens: JWTTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  currentStep: AuthStep;
  signUpEmail: string | null; // Store email during sign-up flow
}

// API Error types
export interface ApiError {
  message: string;
  code?: string;
  field?: string;
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
}

// Password requirements
export interface PasswordRequirements {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
}

// OTP configuration
export interface OTPConfig {
  length: number;
  expiryMinutes: number;
  resendCooldownSeconds: number;
}

// Auth configuration
export interface AuthConfig {
  passwordRequirements: PasswordRequirements;
  otpConfig: OTPConfig;
  tokenExpiryHours: number;
  refreshTokenExpiryDays: number;
}
