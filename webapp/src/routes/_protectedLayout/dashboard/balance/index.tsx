import { createFileRoute } from '@tanstack/react-router';
import { Page } from '@/components/layouts/Page';
import { TotalBalanceCard } from '@/features/balance/components/TotalBalanceCard';
import { WalletCard } from '@/features/balance/components/WalletCard';
import { BankAccountsTable } from '@/features/balance/components/BankAccountsTable';
import { WithdrawWalletsTable } from '@/features/balance/components/WithdrawWalletsTable';
import {
  mockTotalBalance,
  mockWalletBalances,
  mockBankAccounts,
  mockWithdrawWallets
} from '@/features/balance/mockData';

function BalancePage() {
  return (
    <Page
      title="Balance/Account"
    >
      <div className="space-y-8">
        {/* Total Balance */}
        <TotalBalanceCard totalBalance={mockTotalBalance} />

        {/* Wallet Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {mockWalletBalances.map((wallet) => (
            <WalletCard key={wallet.id} wallet={wallet} />
          ))}
        </div>

        {/* Bank Accounts */}
        <BankAccountsTable
          accounts={mockBankAccounts}
          onAddAccount={() => {
            // TODO: Implement add bank account functionality
            console.log('Add bank account');
          }}
        />

        {/* Withdraw Wallets */}
        <WithdrawWalletsTable
          wallets={mockWithdrawWallets}
          onAddWallet={() => {
            // TODO: Implement add withdraw wallet functionality
            console.log('Add withdraw wallet');
          }}
        />
      </div>
    </Page>
  );
}

export const Route = createFileRoute('/_protectedLayout/dashboard/balance/')({
  component: BalancePage,
});
