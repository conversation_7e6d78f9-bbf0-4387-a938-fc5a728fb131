import { createFile<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router';
import { ArrowLeft, Copy } from 'lucide-react';
import { Page } from '@/components/layouts/Page';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { WithdrawHistoryTable } from '@/features/balance/components/WithdrawHistoryTable';
import { getWalletById } from '@/features/balance/mockData';
import { cn } from '@/lib/utils/tw';

// Network colors mapping
const networkColors: Record<string, string> = {
  TRC20: 'bg-green-500',
  ERC20: 'bg-blue-500',
  BEP20: 'bg-yellow-500',
  Polygon: 'bg-purple-500',
  Arbitrum: 'bg-orange-500',
};

function WalletDetailsPage() {
  const { walletId } = Route.useParams();
  const wallet = getWalletById(walletId);

  if (!wallet) {
    return (
      <Page title="Wallet Not Found">
        <div className="text-center py-8">
          <p className="text-muted-foreground">Wallet not found</p>
          <Link to="/dashboard/balance" className="text-primary hover:underline">
            Back to Balance
          </Link>
        </div>
      </Page>
    );
  }

  const networkColor = networkColors[wallet.network] || 'bg-gray-500';

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // TODO: Add toast notification
  };

  return (
    <Page
      title="[Wallet]"
    >
      <div className="space-y-8">
        {/* Back Button */}
        <Link to="/dashboard/balance">
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </Link>

        {/* Wallet Details Card */}
        <Card className="bg-card/50 border-border/50">
          <CardContent className="p-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Wallet Info */}
              <div className="lg:col-span-2 space-y-6">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className={cn(
                      'w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-xl',
                      networkColor
                    )}>
                      {wallet.coinName.charAt(0)}
                    </div>
                    <div className={cn(
                      'absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-background',
                      networkColor
                    )} />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-foreground">
                      {wallet.coinName}
                    </h2>
                    <p className="text-muted-foreground">{wallet.network}</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Wallet address
                    </label>
                    <div className="flex items-center gap-2 mt-1">
                      <code className="text-foreground font-mono text-sm bg-muted px-2 py-1 rounded">
                        {wallet.walletAddress}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(wallet.walletAddress)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Network
                    </label>
                    <p className="text-foreground font-medium">{wallet.network}</p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Balance
                    </label>
                    <div className="space-y-1">
                      <p className="text-2xl font-bold text-foreground">
                        {wallet.balance.toFixed(2)} {wallet.coinName}
                      </p>
                      <p className="text-muted-foreground">
                        {wallet.fiatEquivalent.toFixed(4)} AED
                      </p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4">
                  <Button className="bg-primary/10 text-primary hover:bg-primary/20">
                    Withdraw to a wallet
                  </Button>
                  <Button className="bg-primary/10 text-primary hover:bg-primary/20">
                    Withdraw to bank account
                  </Button>
                </div>
              </div>

              {/* QR Code */}
              <div className="flex justify-center lg:justify-end">
                <div className="w-48 h-48 bg-muted rounded-lg flex items-center justify-center">
                  {wallet.qrCode ? (
                    <img
                      src={wallet.qrCode}
                      alt="QR Code"
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    <div className="w-32 h-32 bg-background rounded border-2 border-dashed border-border flex items-center justify-center">
                      <span className="text-muted-foreground text-sm">QR Code</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Withdraw History */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">Withdraw history</h3>
          <WithdrawHistoryTable history={wallet.withdrawHistory} />
        </div>
      </div>
    </Page>
  );
}

export const Route = createFileRoute('/_protectedLayout/dashboard/balance/wallet/$walletId')({
  component: WalletDetailsPage,
});
