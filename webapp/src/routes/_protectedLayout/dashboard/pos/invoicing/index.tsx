import { But<PERSON> } from '@/components/ui/button';
import { createFileRoute } from '@tanstack/react-router';
import { Link } from '@tanstack/react-router';
import { InvoicesTable } from '@/features/dashboard/components/invoices/InvoicesTable';
import { useInvoicesQuery } from '@/features/dashboard/hooks/useInvoicesQuery';
import { InvoiceColumns } from '@/features/dashboard/components/invoices/InvoiceTableColumns';

export const Route = createFileRoute('/_protectedLayout/dashboard/pos/invoicing/')({
  component: RootComponent,
});

function RootComponent() {
  const { data: invoices, isLoading, isError } = useInvoicesQuery();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">Invoicing</h2>
        <Button asChild>
          <Link to="/dashboard/pos/invoicing/new">Create Invoice</Link>
        </Button>
      </div>

      <InvoicesTable
        columns={InvoiceColumns}
        data={invoices ?? []}
        title="Invoices"
        isLoading={isLoading}
        isError={isError}
      />
    </div>
  );
}
