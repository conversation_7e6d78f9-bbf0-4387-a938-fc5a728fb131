import { Button } from '@/components/ui/button';
import { createFileRoute } from '@tanstack/react-router';
import { Link } from '@tanstack/react-router';
import { InvoicesTable } from '@/features/dashboard/components/invoices/InvoicesTable';
import { InvoiceColumns } from '@/features/dashboard/components/invoices/InvoiceTableColumns';
import { Page } from '@/components/layouts/Page';
import { ArrowLeft } from 'lucide-react';

export const Route = createFileRoute('/_protectedLayout/dashboard/pos/invoicing/')({
  component: RootComponent,
});

function RootComponent() {
  return (
    <Page
      title={
        <div className="flex items-center gap-3">
          <Link to="/dashboard/pos" className="text-muted-foreground hover:text-foreground">
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <span>Invoicing</span>
        </div>
      }
      actions={
        <Button className="bg-primary text-primary-foreground hover:bg-primary/90">
          <Link to="/dashboard/pos/invoicing/new" className="flex items-center gap-2">
            + Create invoice
          </Link>
        </Button>
      }
    >
      {/* Breadcrumb */}
      <div className="text-sm text-muted-foreground mb-6">
        Dashboard &gt; POS &gt; Invoicing
      </div>

      <InvoicesTable columns={InvoiceColumns} />
    </Page>
  );
}
