import { createFileRoute, notFound } from '@tanstack/react-router';
import { InvoiceDetailsPage } from '@/features/dashboard/components/invoices/InvoiceDetailsPage';
import { useInvoicesQuery } from '@/features/dashboard/hooks/useInvoicesQuery';
import { LoadingState } from '@/features/dashboard/components/LoadingState';
import { Page } from '@/components/layouts/Page';

export const Route = createFileRoute('/_protectedLayout/dashboard/pos/invoicing/$invoiceId')({
  component: InvoiceDetailsComponent,
});

function InvoiceDetailsComponent() {
  const { invoiceId } = Route.useParams();
  const { data: invoices, isLoading, isError } = useInvoicesQuery();

  if (isLoading) {
    return (
      <Page title="Loading Invoice...">
        <LoadingState />
      </Page>
    );
  }

  if (isError) {
    return (
      <Page title="Error">
        <div className="flex items-center justify-center h-64">
          <p className="text-destructive">Failed to load invoice. Please try again.</p>
        </div>
      </Page>
    );
  }

  const invoice = invoices?.find(inv => inv.invoice_id === invoiceId);

  if (!invoice) {
    throw notFound();
  }

  return <InvoiceDetailsPage invoice={invoice} />;
}
