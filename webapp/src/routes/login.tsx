import { AuthContainer } from '@/features/auth/components/AuthContainer';
import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { useAppDispatch } from '@/store/hooks';
import { setCurrentStep } from '@/store/slices/authSlice';
import { useEffect } from 'react';

export const Route = createFileRoute('/login')({
  component: RouteComponent,
});

function RouteComponent() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  // Set initial step to sign-in when accessing this route
  useEffect(() => {
    dispatch(setCurrentStep('sign-in'));
  }, [dispatch]);

  const handleAuthSuccess = () => {
    // Redirect to dashboard after successful authentication
    navigate({ to: '/dashboard' });
  };

  return (
    <div className="min-h-screen w-screen flex items-center justify-center bg-[#0a0a0a] p-4">
      {/* Background gradient effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-[#C4FF61]/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-[#C4FF61]/5 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-[#C4FF61]/5 to-transparent rounded-full blur-3xl" />
      </div>

      {/* Auth Container */}
      <div className="relative z-10 w-full flex justify-center">
        <AuthContainer onSuccess={handleAuthSuccess} />
      </div>
    </div>
  );
}
