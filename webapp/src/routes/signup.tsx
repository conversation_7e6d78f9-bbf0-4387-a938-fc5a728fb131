import { AuthContainer } from '@/features/auth/components/AuthContainer';
import { useAppDispatch } from '@/store/hooks';
import { setCurrentStep } from '@/store/slices/authSlice';
import { createFileRoute } from '@tanstack/react-router';
import { useEffect } from 'react';

export const Route = createFileRoute('/signup')({
  component: RouteComponent,
});

function RouteComponent() {
  const dispatch = useAppDispatch();

  // Set initial step to sign-up when accessing this route
  useEffect(() => {
    dispatch(setCurrentStep('sign-up'));
  }, [dispatch]);

  return (
    <div className="min-h-screen w-screen flex items-center justify-center bg-black p-4">
      {/* Background gradient effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 right-0 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl" />
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-pink-500/20 rounded-full blur-3xl" />
      </div>
      
      {/* Auth Container */}
      <div className="relative z-10">
        <AuthContainer />
      </div>
    </div>
  );
}
