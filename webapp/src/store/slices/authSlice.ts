import { PayloadAction, createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import {
  AuthState,
  User,
  JWTTokens,
  AuthStep,
  SignUpFormData,
  SignInFormData,
  OTPFormData,
  BusinessDetailsFormData,
} from '@/features/auth/types';
import {
  signUpApi,
  signInApi,
  verifyOTPApi,
  resendOTPApi,
  submitBusinessDetailsApi,
  refreshTokenApi,
  logoutApi,
} from '@/features/auth/api/mockAuthApi';

const initialState: AuthState = {
  user: null,
  tokens: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  currentStep: 'sign-in',
  signUpEmail: null,
};

// Async thunks for auth operations
export const signUp = createAsyncThunk(
  'auth/signUp',
  async (data: SignUpFormData, { rejectWithValue }) => {
    const response = await signUpApi(data);
    if (!response.success) {
      return rejectWithValue(response.error || 'Sign up failed');
    }
    return { ...response.data!, email: data.email };
  }
);

export const signIn = createAsyncThunk(
  'auth/signIn',
  async (data: SignInFormData, { rejectWithValue }) => {
    const response = await signInApi(data);
    if (!response.success) {
      return rejectWithValue(response.error || 'Sign in failed');
    }
    return response.data!;
  }
);

export const verifyOTP = createAsyncThunk(
  'auth/verifyOTP',
  async ({ email, code }: { email: string; code: OTPFormData }, { rejectWithValue }) => {
    const response = await verifyOTPApi(email, code);
    if (!response.success) {
      return rejectWithValue(response.error || 'OTP verification failed');
    }
    return response.data!;
  }
);

export const resendOTP = createAsyncThunk(
  'auth/resendOTP',
  async (email: string, { rejectWithValue }) => {
    const response = await resendOTPApi(email);
    if (!response.success) {
      return rejectWithValue(response.error || 'Failed to resend OTP');
    }
    return response.data!;
  }
);

export const submitBusinessDetails = createAsyncThunk(
  'auth/submitBusinessDetails',
  async ({ email, data }: { email: string; data: BusinessDetailsFormData }, { rejectWithValue }) => {
    const response = await submitBusinessDetailsApi(email, data);
    if (!response.success) {
      return rejectWithValue(response.error || 'Failed to submit business details');
    }
    return response.data!;
  }
);

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { getState, rejectWithValue }) => {
    const state = getState() as RootState;
    const refreshTokenValue = state.auth.tokens?.refreshToken;

    if (!refreshTokenValue) {
      return rejectWithValue('No refresh token available');
    }

    const response = await refreshTokenApi(refreshTokenValue);
    if (!response.success) {
      return rejectWithValue(response.error || 'Token refresh failed');
    }
    return response.data!;
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    const response = await logoutApi();
    if (!response.success) {
      return rejectWithValue(response.error || 'Logout failed');
    }
    return response.data!;
  }
);

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Legacy action for backward compatibility
    setUser: (state, action: PayloadAction<User | null>) => {
      state.user = action.payload;
      state.isAuthenticated = action.payload !== null;
    },
    setTokens: (state, action: PayloadAction<JWTTokens | null>) => {
      state.tokens = action.payload;
    },
    setCurrentStep: (state, action: PayloadAction<AuthStep>) => {
      state.currentStep = action.payload;
    },
    setSignUpEmail: (state, action: PayloadAction<string | null>) => {
      state.signUpEmail = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetAuthState: (state) => {
      state.user = null;
      state.tokens = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.error = null;
      state.currentStep = 'sign-in';
      state.signUpEmail = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Sign Up
      .addCase(signUp.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(signUp.fulfilled, (state, action) => {
        state.isLoading = false;
        state.signUpEmail = action.payload.email;
        state.currentStep = 'otp-verification';
      })
      .addCase(signUp.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Sign In
      .addCase(signIn.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(signIn.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.tokens = action.payload.tokens;
        state.isAuthenticated = true;
        state.currentStep = 'completed';
        // Save tokens to localStorage
        if (typeof window !== 'undefined') {
          import('@/features/auth/utils/tokenManager').then(({ tokenStorage }) => {
            tokenStorage.setTokens(action.payload.tokens);
          });
        }
      })
      .addCase(signIn.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // OTP Verification
      .addCase(verifyOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyOTP.fulfilled, (state) => {
        state.isLoading = false;
        state.currentStep = 'email-confirmed';
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Resend OTP
      .addCase(resendOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resendOTP.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(resendOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Business Details
      .addCase(submitBusinessDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(submitBusinessDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.currentStep = 'completed';
        // Note: Tokens will be set when user signs in after registration
      })
      .addCase(submitBusinessDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Refresh Token
      .addCase(refreshToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.isLoading = false;
        state.tokens = action.payload;
        // Save new tokens to localStorage
        if (typeof window !== 'undefined') {
          import('@/features/auth/utils/tokenManager').then(({ tokenStorage }) => {
            tokenStorage.setTokens(action.payload);
          });
        }
      })
      .addCase(refreshToken.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.tokens = null;
        state.isAuthenticated = false;
        state.currentStep = 'sign-in';
        // Clear tokens from localStorage
        if (typeof window !== 'undefined') {
          import('@/features/auth/utils/tokenManager').then(({ tokenStorage }) => {
            tokenStorage.clearTokens();
          });
        }
      })

      // Logout
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.user = null;
        state.tokens = null;
        state.isAuthenticated = false;
        state.currentStep = 'sign-in';
        state.signUpEmail = null;
        state.error = null;
        // Clear tokens from localStorage
        if (typeof window !== 'undefined') {
          import('@/features/auth/utils/tokenManager').then(({ tokenStorage }) => {
            tokenStorage.clearTokens();
          });
        }
      })
      .addCase(logout.rejected, (state) => {
        state.isLoading = false;
        // Even if logout fails, clear local state
        state.user = null;
        state.tokens = null;
        state.isAuthenticated = false;
        state.currentStep = 'sign-in';
        state.signUpEmail = null;
        // Clear tokens from localStorage
        if (typeof window !== 'undefined') {
          import('@/features/auth/utils/tokenManager').then(({ tokenStorage }) => {
            tokenStorage.clearTokens();
          });
        }
      });
  },
});

// Selectors
export const selectIsAuthenticated = (state: RootState): boolean => state.auth.isAuthenticated;
export const selectUser = (state: RootState): User | null => state.auth.user;
export const selectTokens = (state: RootState): JWTTokens | null => state.auth.tokens;
export const selectAuthLoading = (state: RootState): boolean => state.auth.isLoading;
export const selectAuthError = (state: RootState): string | null => state.auth.error;
export const selectCurrentStep = (state: RootState): AuthStep => state.auth.currentStep;
export const selectSignUpEmail = (state: RootState): string | null => state.auth.signUpEmail;

export const {
  setUser,
  setTokens,
  setCurrentStep,
  setSignUpEmail,
  clearError,
  resetAuthState
} = authSlice.actions;

export default authSlice.reducer;
