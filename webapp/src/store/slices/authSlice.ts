import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { RootState } from '..';

interface AuthState {
  user: null | {
    id: string;
    email: string;
  };
}

const initialState: AuthState = {
  user: null,
};

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<AuthState['user']>) => {
      state.user = action.payload;
    },
    logout: (state) => {
      state.user = null;
    },
  },
});

// Selector to compute authentication status
export const selectIsAuthenticated = (state: RootState): boolean => state.auth.user !== null;

export const { setUser, logout } = authSlice.actions;
export default authSlice.reducer;
