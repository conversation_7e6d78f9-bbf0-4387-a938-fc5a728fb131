import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from './table';

describe('Table Components', () => {
  describe('Table', () => {
    it('renders with children', () => {
      render(
        <Table>
          <TableBody>
            <TableRow>
              <TableCell>Cell content</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      );
      
      const table = screen.getByRole('table');
      expect(table).toBeInTheDocument();
      expect(screen.getByText('Cell content')).toBeInTheDocument();
    });
  });

  describe('TableHeader', () => {
    it('renders as thead', () => {
      render(
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Header</TableHead>
            </TableRow>
          </TableHeader>
        </Table>
      );
      
      const header = screen.getByText('Header');
      expect(header.closest('thead')).toBeInTheDocument();
    });
  });

  describe('TableBody', () => {
    it('renders as tbody', () => {
      render(
        <Table>
          <TableBody>
            <TableRow>
              <TableCell>Body content</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      );
      
      const cell = screen.getByText('Body content');
      expect(cell.closest('tbody')).toBeInTheDocument();
    });
  });

  describe('TableRow', () => {
    it('renders as tr', () => {
      render(
        <Table>
          <TableBody>
            <TableRow>
              <TableCell>Row content</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      );
      
      const cell = screen.getByText('Row content');
      expect(cell.closest('tr')).toBeInTheDocument();
    });
  });

  describe('TableHead', () => {
    it('renders as th', () => {
      render(
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Column Header</TableHead>
            </TableRow>
          </TableHeader>
        </Table>
      );
      
      const header = screen.getByRole('columnheader');
      expect(header).toBeInTheDocument();
      expect(header).toHaveTextContent('Column Header');
    });
  });

  describe('TableCell', () => {
    it('renders as td', () => {
      render(
        <Table>
          <TableBody>
            <TableRow>
              <TableCell>Cell data</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      );
      
      const cell = screen.getByRole('cell');
      expect(cell).toBeInTheDocument();
      expect(cell).toHaveTextContent('Cell data');
    });
  });

  describe('TableCaption', () => {
    it('renders as caption', () => {
      render(
        <Table>
          <TableCaption>Table Caption</TableCaption>
          <TableBody>
            <TableRow>
              <TableCell>Data</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      );
      
      const caption = screen.getByText('Table Caption');
      expect(caption.tagName.toLowerCase()).toBe('caption');
    });
  });

  describe('Complete Table', () => {
    it('renders full table structure', () => {
      render(
        <Table>
          <TableCaption>A list of users</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>John Doe</TableCell>
              <TableCell><EMAIL></TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Jane Smith</TableCell>
              <TableCell><EMAIL></TableCell>
            </TableRow>
          </TableBody>
        </Table>
      );

      expect(screen.getByText('A list of users')).toBeInTheDocument();
      expect(screen.getByText('Name')).toBeInTheDocument();
      expect(screen.getByText('Email')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });
});
