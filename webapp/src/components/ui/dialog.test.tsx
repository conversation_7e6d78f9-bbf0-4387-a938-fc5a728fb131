import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './dialog';

describe('Dialog Components', () => {
  describe('Dialog', () => {
    it('renders trigger and opens dialog', () => {
      render(
        <Dialog>
          <DialogTrigger asChild>
            <button>Open Dialog</button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Dialog Title</DialogTitle>
              <DialogDescription>Dialog Description</DialogDescription>
            </DialogHeader>
            <p>Dialog content</p>
          </DialogContent>
        </Dialog>
      );

      const trigger = screen.getByRole('button', { name: 'Open Dialog' });
      expect(trigger).toBeInTheDocument();

      fireEvent.click(trigger);
      expect(screen.getByText('Dialog Title')).toBeInTheDocument();
      expect(screen.getByText('Dialog Description')).toBeInTheDocument();
      expect(screen.getByText('Dialog content')).toBeInTheDocument();
    });

    it('handles controlled state', () => {
      const onOpenChange = vi.fn();
      
      render(
        <Dialog open={true} onOpenChange={onOpenChange}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Controlled Dialog</DialogTitle>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      );

      expect(screen.getByText('Controlled Dialog')).toBeInTheDocument();
    });
  });

  describe('DialogTrigger', () => {
    it('renders as child component', () => {
      render(
        <Dialog>
          <DialogTrigger asChild>
            <button>Custom Trigger</button>
          </DialogTrigger>
          <DialogContent>Content</DialogContent>
        </Dialog>
      );

      expect(screen.getByRole('button', { name: 'Custom Trigger' })).toBeInTheDocument();
    });
  });

  describe('DialogContent', () => {
    it('renders with children when dialog is open', () => {
      render(
        <Dialog open={true}>
          <DialogContent>
            <div>Dialog Content</div>
          </DialogContent>
        </Dialog>
      );

      expect(screen.getByText('Dialog Content')).toBeInTheDocument();
    });
  });

  describe('DialogHeader', () => {
    it('renders with children', () => {
      render(
        <Dialog open={true}>
          <DialogContent>
            <DialogHeader>
              <span>Header Content</span>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      );

      expect(screen.getByText('Header Content')).toBeInTheDocument();
    });
  });

  describe('DialogTitle', () => {
    it('renders as heading', () => {
      render(
        <Dialog open={true}>
          <DialogContent>
            <DialogTitle>Test Title</DialogTitle>
          </DialogContent>
        </Dialog>
      );

      const title = screen.getByRole('heading', { name: 'Test Title' });
      expect(title).toBeInTheDocument();
    });
  });

  describe('DialogDescription', () => {
    it('renders description text', () => {
      render(
        <Dialog open={true}>
          <DialogContent>
            <DialogDescription>Test Description</DialogDescription>
          </DialogContent>
        </Dialog>
      );

      expect(screen.getByText('Test Description')).toBeInTheDocument();
    });
  });
});
