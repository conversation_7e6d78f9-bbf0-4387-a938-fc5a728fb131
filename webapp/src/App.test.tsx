import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import App from './App';

describe('App', () => {
  it('renders without crashing', () => {
    const { container } = render(<App />);
    expect(container).toBeInTheDocument();
  });

  it('provides QueryClient and Router', () => {
    const { container } = render(<App />);
    expect(container.firstChild).toBeTruthy();
  });
});
