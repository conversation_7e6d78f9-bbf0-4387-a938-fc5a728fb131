# Инструкции по тестированию аутентификации

## Тестирование входа с моковыми данными

1. Перейдите на страницу входа: http://localhost:3000/login
2. Используйте следующие тестовые данные:
   - **Email**: `<EMAIL>`
   - **Password**: `demo123`
3. Нажмите "Continue"
4. Вы должны автоматически попасть в дашборд

## Тестирование полного процесса регистрации

1. Перейдите на страницу регистрации: http://localhost:3000/signup
2. Заполните форму регистрации:
   - **Email**: любой валидный email (например, `<EMAIL>`)
   - **Password**: минимум 8 символов с заглавной буквой, строчной буквой и цифрой (например, `Password123`)
3. Нажмите "Continue"
4. Введите OTP код: `123456` (фиксированный код для тестирования)
5. Нажмите "Verify"
6. На экране подтверждения email нажмите "Continue"
7. На экране создания кошелька:
   - Выберите монету (Tether уже выбран по умолчанию)
   - Выберите сеть (ERC-20 выбран по умолчанию)
   - Поставьте галочку "I agree to the Terms and Conditions"
   - Нажмите "Continue"
8. **Результат**: После создания кошелька вы должны автоматически попасть в дашборд, а НЕ на форму входа

## Что было исправлено

1. **Автоматический вход после регистрации**: Теперь после создания кошелька пользователь автоматически аутентифицируется и попадает в дашборд
2. **Правильная обработка токенов**: Токены сохраняются в localStorage
3. **Корректные редиректы**: И вход, и регистрация правильно перенаправляют в дашборд
4. **Моковые данные**: Подтверждено, что вход с `<EMAIL>` / `demo123` работает

## Ожидаемое поведение

- ✅ Вход с моковыми данными должен работать
- ✅ Регистрация должна завершаться автоматическим входом в дашборд
- ✅ Никаких дополнительных форм входа после завершения регистрации
- ✅ Токены должны сохраняться в localStorage
